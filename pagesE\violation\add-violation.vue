<template>
	<view class="container">
		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 车牌信息区域 -->
			<view class="section-card">
				<view class="section-header">
					<view class="header-icon">
						<text class="icon-emoji">🚗</text>
					</view>
					<text class="section-title">车牌信息</text>
				</view>
				<view class="input-group">
					<!-- 车牌号输入区域 -->
					<view class="plate-input-section">
						<!-- 搜索框和识别按钮的容器 -->
						<view class="input-actions-container">
							<!-- 搜索框容器 -->
							<view class="plate-search-container">
								<view class="search-input-wrapper">
									<input
										class="plate-search-input"
										v-model="formData.plateNumber"
										placeholder="请输入车牌号码"
										@input="onPlateNumberInput"
										@focus="onPlateSearchFocus"
										@blur="onPlateSearchBlur"
										maxlength="8"
									/>
									<view class="search-icon" v-if="!formData.plateNumber">
										<u-icon name="search" size="20" color="#c8c9cc"></u-icon>
									</view>
									<view class="clear-btn" v-if="formData.plateNumber" @click="clearPlateNumber">
										<u-icon name="close-circle-fill" size="20" color="#c8c9cc"></u-icon>
									</view>
								</view>

								<!-- 搜索建议 -->
								<view class="plate-suggestions" v-if="showPlateSuggestions && plateSuggestions.length > 0">
									<view
										class="suggestion-item"
										v-for="(suggestion, index) in plateSuggestions"
										:key="index"
										@click="selectPlateSuggestion(suggestion)"
									>
										<view class="suggestion-icon">
											<text class="icon-emoji">🚗</text>
										</view>
										<text class="suggestion-text">{{ suggestion.plateNumber }}</text>
										<text class="suggestion-owner" v-if="suggestion.ownerName">{{ suggestion.ownerName }}</text>
									</view>
								</view>
							</view>

							<!-- 车牌识别按钮 -->
							<view class="recognition-btn-container">
								<view class="plate-recognition-btn" @click="openPlateRecognition">
									<u-icon name="camera" size="24" color="#2979ff"></u-icon>
									<text class="btn-label">识别</text>
								</view>
							</view>
						</view>
					</view>
					<!-- 车主信息显示 -->
					<view class="owner-info" v-if="ownerInfo">
						<view class="info-item">
							<text class="info-label">车主：</text>
							<text class="info-value">{{ ownerInfo.name }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">电话：</text>
							<text class="info-value">{{ ownerInfo.phone }}</text>
						</view>
						<view class="info-item" v-if="ownerInfo.address">
							<text class="info-label">住址：</text>
							<text class="info-value">{{ ownerInfo.address }}</text>
						</view>
						<view class="info-item" v-if="ownerInfo.creditScore">
							<text class="info-label">信用分：</text>
							<text class="info-value" :class="creditScoreClass">
								{{ ownerInfo.creditScore }}分
							</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 违规类型区域 -->
			<view class="section-card">
				<view class="section-header">
					<view class="header-icon">
						<text class="icon-emoji">⚠️</text>
					</view>
					<text class="section-title">违规类型</text>
				</view>
				<view class="violation-types">
					<!-- 已选择的类型显示 -->
					<view class="selected-type" v-if="selectedTypeInfo">
						<view class="selected-display">
							<text class="selected-icon">{{ selectedTypeInfo.icon }}</text>
							<text class="selected-name">{{ selectedTypeInfo.name }}</text>
							<text class="selected-check">✓</text>
						</view>
						<view class="divider"></view>
					</view>

					<!-- 搜索框 -->
					<view class="search-section" v-if="uiState.isSearching">
						<view class="search-container">
							<view class="search-box">
								<text class="search-icon">🔍</text>
								<input class="search-input" v-model="uiState.searchKeyword" placeholder="搜索违规类型..."
									@input="onSearchInput" focus />
								<text class="search-clear" v-if="uiState.searchKeyword" @click="clearSearch">×</text>
							</view>
							<view class="search-cancel" @click="cancelSearch">
								<text class="cancel-text">取消</text>
							</view>
						</view>
					</view>

					<!-- 搜索结果 -->
					<view class="search-results" v-if="uiState.isSearching && uiState.searchKeyword">
						<view class="section-label">搜索结果</view>
						<view class="type-tags" v-if="searchResults.length > 0">
							<view class="type-tag" v-for="(type, index) in searchResults" :key="index"
								@click="selectType(type)">
								<text class="tag-icon">{{ type.icon }}</text>
								<text class="tag-text">{{ type.name }}</text>
							</view>
						</view>
						<!-- 无搜索结果提示 -->
						<view class="no-results" v-if="searchResults.length === 0">
							<text class="no-results-icon">🔍</text>
							<text class="no-results-text">未找到匹配的违规类型</text>
							<text class="no-results-tip">试试其他关键词或选择下方常用类型</text>
						</view>
						<view class="divider" v-if="searchResults.length > 0"></view>
					</view>

					<!-- 常用类型 -->
					<view class="common-section">
						<view class="section-label">{{ selectedTypeInfo ? '其他常用类型' : '常用类型' }}</view>
						<view class="type-tags">
							<view class="type-tag" v-for="(type, index) in displayCommonTypes" :key="index"
								@click="selectType(type)">
								<text class="tag-icon">{{ type.icon }}</text>
								<text class="tag-text">{{ type.name }}</text>
							</view>
							<view class="type-tag more" @click="toggleMoreTypes">
								<text class="tag-text">{{ uiState.showMoreTypes ? '收起 ▲' : '更多类型 ▼' }}</text>
							</view>
						</view>
					</view>

					<!-- 其他类型（展开时显示） -->
					<view class="others-section" v-if="uiState.showMoreTypes">
						<view class="divider"></view>
						<view class="section-label">其他类型</view>
						<view class="type-tags">
							<view class="type-tag" v-for="(type, index) in violationConfig.others" :key="index"
								:class="{ selected: formData.violationType === type.value }"
								@click="selectType(type)">
								<text class="tag-icon">{{ type.icon }}</text>
								<text class="tag-text">{{ type.name }}</text>
							</view>
						</view>
					</view>

					<!-- 搜索入口 -->
					<view class="find-entry" v-if="!uiState.isSearching">
						<view class="divider"></view>
						<view class="find-trigger" @click="startSearch">
							<text class="find-icon">🔍</text>
							<text class="find-text">查找其他违规类型...</text>
						</view>
					</view>

					<!-- 自定义违规类型输入 -->
					<view class="custom-type-input" v-if="formData.violationType === 'other'">
						<input class="custom-input" v-model="formData.customType" placeholder="请输入自定义违规类型"
							maxlength="50" />
					</view>
				</view>
			</view>

			<!-- 违规位置区域 -->
			<view class="section-card">
				<view class="section-header">
					<view class="header-icon">
						<text class="icon-emoji">📍</text>
					</view>
					<text class="section-title">违规位置</text>
				</view>
				<view class="location-input-group">
					<view class="location-wrapper">
						<input class="location-input" v-model="formData.location" placeholder="请输入违规位置"
							maxlength="100" @input="onLocationInput" @focus="onLocationFocus" @blur="onLocationBlur" />
						<view class="location-btn" @click="getCurrentLocation">
							<text class="icon-emoji">🎯</text>
							<text class="btn-text">定位</text>
						</view>
					</view>

					<!-- 位置建议 -->
					<view class="location-suggestions" v-if="showLocationSuggestions && locationSuggestions.length > 0">
						<view
							class="location-suggestion-item"
							v-for="(location, index) in locationSuggestions"
							:key="index"
							@click="selectLocationSuggestion(location)"
						>
							<view class="suggestion-icon">
								<text class="icon-emoji">📍</text>
							</view>
							<text class="suggestion-text">{{ location }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 现场取证区域 -->
			<view class="section-card">
				<view class="section-header">
					<view class="header-icon">
						<text class="icon-emoji">📸</text>
					</view>
					<text class="section-title">现场取证</text>
				</view>
				<view class="evidence-section">
					<!-- 照片上传 -->
					<view class="photo-upload">
						<view class="upload-header">
							<text class="upload-title">拍照取证</text>
							<text class="photo-count">{{ formData.photos.length }}/6</text>
						</view>
						<view class="photo-grid">
							<view class="photo-item" v-for="(photo, index) in formData.photos" :key="index"
								@click="previewPhoto(index)">
								<image :src="photo" mode="aspectFill" class="photo-image"></image>
								<view class="photo-delete" @click.stop="deletePhoto(index)">
									<text class="icon-emoji">×</text>
								</view>
							</view>
							<view class="photo-add" v-if="formData.photos.length < 6" @click="takePhoto">
								<text class="icon-emoji add-icon">📷</text>
								<text class="add-text">拍照</text>
							</view>
						</view>
					</view>


				</view>
			</view>

			<!-- 违规描述区域 -->
			<view class="section-card">
				<view class="section-header">
					<view class="header-icon">
						<text class="icon-emoji">📝</text>
					</view>
					<text class="section-title">违规描述</text>
				</view>
				<view class="description-input">
					<textarea class="description-textarea" v-model="formData.description"
						placeholder="请详细描述违规情况..." maxlength="200" :show-word-limit="true"
						:auto-height="true"></textarea>

					<!-- 快速模板 -->
					<view class="description-templates" v-if="descriptionTemplates.length > 0">
						<text class="template-label">快速模板：</text>
						<view class="template-tags">
							<view
								class="template-tag"
								v-for="(template, index) in descriptionTemplates"
								:key="index"
								@click="useDescriptionTemplate(template)"
							>
								<text class="template-text">{{ template }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<view class="submit-btn" :class="{ disabled: !canSubmit }" @click="submitViolation">
					<text>{{ submitting ? '提交中...' : '提交违规记录' }}</text>
				</view>
			</view>
		</view>

		<!-- 车牌识别弹窗 -->
		<view class="scan-modal" v-if="showScanModal" @click="closeScanModal">
			<view class="scan-content" @click.stop>
				<view class="scan-header">
					<text class="scan-title">车牌识别</text>
					<view class="scan-close" @click="closeScanModal">
						<text class="icon-emoji">×</text>
					</view>
				</view>

				<!-- 摄像头识别界面 -->
				<view class="camera-container" v-if="showCamera">
					<camera
						device-position="back"
						flash="off"
						@error="handleCameraError"
						class="camera-preview"
						ref="camera"
					>
						<!-- 车牌框选区域 -->
						<view class="plate-frame">
							<view class="frame-corner tl"></view>
							<view class="frame-corner tr"></view>
							<view class="frame-corner bl"></view>
							<view class="frame-corner br"></view>
							<text class="frame-text">自动识别中，请保持稳定</text>
						</view>

						<!-- 自动识别状态指示器 -->
						<view class="auto-status">
							<view class="status-dot"></view>
							<text class="status-text">自动识别: {{ autoRecognizeCount }}次</text>
						</view>
					</camera>

					<!-- 摄像头控制按钮 -->
					<view class="camera-controls">
						<!-- 手动拍照按钮 -->
						<button @tap="capturePhoto" :disabled="isRecognizing" class="capture-btn">
							<text class="camera-icon">📷</text>
							{{ isRecognizing ? '识别中...' : '手动拍照' }}
						</button>

						<button @tap="closeCamera" class="close-btn">
							<text class="close-icon">❌</text>
							关闭摄像头
						</button>
					</view>

					<!-- 加载提示 -->
					<view class="loading-overlay" v-if="isRecognizing">
						<view class="loading-content">
							<text class="loading-text">正在识别车牌...</text>
						</view>
					</view>
				</view>

				<!-- 功能选择界面 -->
				<view class="function-buttons" v-if="!showCamera">
					<view class="scan-area">
						<view class="scan-frame">
							<view class="scan-line"></view>
						</view>
						<text class="scan-tip">选择识别方式</text>
					</view>

					<view class="scan-result" v-if="scanResult">
						<text class="result-label">识别结果：</text>
						<text class="result-text">{{ scanResult }}</text>
					</view>

					<view class="scan-actions">
						<view class="scan-action-btn primary" @click="startCamera">
							<text>📷 摄像头识别</text>
						</view>
						<view class="scan-action-btn" @click="chooseFromAlbum">
							<text>🖼️ 相册识别</text>
						</view>
						<view class="scan-action-btn primary" @click="useScanResult" v-if="scanResult">
							<text>使用结果</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 提交确认弹窗 -->
		<view class="confirm-modal" v-if="showConfirmModal" @click="closeConfirmModal">
			<view class="confirm-content" @click.stop>
				<view class="confirm-header">
					<text class="confirm-title">确认提交</text>
				</view>
				<view class="confirm-body">
					<text class="confirm-text">确认提交违规记录吗？</text>
					<view class="confirm-info">
						<text class="info-text">车牌：{{ formData.plateNumber }}</text>
						<text class="info-text">类型：{{ getViolationTypeName() }}</text>
						<text class="info-text">位置：{{ formData.location }}</text>
					</view>
				</view>
				<view class="confirm-actions">
					<view class="confirm-btn cancel" @click="closeConfirmModal">
						<text>取消</text>
					</view>
					<view class="confirm-btn primary" @click="confirmSubmit">
						<text>{{ submitting ? '提交中...' : '确认提交' }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 车牌识别弹窗 -->
		<u-modal
			:show="showPlateRecognitionModal"
			title="车牌识别"
			:show-cancel-button="true"
			:show-confirm-button="false"
			@cancel="closePlateRecognition"
		>
			<view class="plate-recognition-modal">
				<!-- 摄像头识别界面 -->
				<view class="camera-container" v-if="showCamera">
					<camera
						device-position="back"
						flash="off"
						@error="handleCameraError"
						class="camera-preview"
						ref="camera"
					>
						<!-- 车牌框选区域 -->
						<view class="plate-frame">
							<view class="frame-corner tl"></view>
							<view class="frame-corner tr"></view>
							<view class="frame-corner bl"></view>
							<view class="frame-corner br"></view>
							<text class="frame-text">请将车牌对准框内</text>
						</view>

						<!-- 自动识别状态指示器 -->
						<view class="auto-status" v-if="autoRecognize">
							<view class="status-dot"></view>
							<text class="status-text">自动识别: {{ autoRecognizeCount }}次</text>
						</view>
					</camera>

					<!-- 摄像头控制按钮 -->
					<view class="camera-controls">
						<!-- 手动拍照按钮 -->
						<view class="control-btn capture-btn" @click="capturePhoto" :class="{ disabled: isRecognizing }">
							<u-icon name="camera" size="24" color="#fff"></u-icon>
							<text>{{ isRecognizing ? '识别中...' : '拍照识别' }}</text>
						</view>

						<!-- 自动识别开关 -->
						<view class="control-btn auto-btn" @click="toggleAutoRecognize" :class="{ active: autoRecognize }">
							<u-icon name="play-circle" size="24" color="#fff" v-if="!autoRecognize"></u-icon>
							<u-icon name="pause-circle" size="24" color="#fff" v-if="autoRecognize"></u-icon>
							<text>{{ autoRecognize ? '停止自动' : '自动识别' }}</text>
						</view>

						<!-- 关闭摄像头 -->
						<view class="control-btn close-btn" @click="closeCamera">
							<u-icon name="close" size="24" color="#fff"></u-icon>
							<text>关闭</text>
						</view>
					</view>

					<!-- 加载提示 -->
					<view class="loading-overlay" v-if="isRecognizing">
						<view class="loading-content">
							<u-loading-icon mode="circle" color="#2979ff" size="40"></u-loading-icon>
							<text class="loading-text">正在识别车牌...</text>
						</view>
					</view>
				</view>

				<!-- 识别选择界面 -->
				<view class="recognition-options" v-if="!showCamera && !recognitionResult">
					<view class="option-item" @click="openCamera">
						<view class="option-icon">
							<u-icon name="camera" size="40" color="#2979ff"></u-icon>
						</view>
						<view class="option-content">
							<text class="option-title">摄像头识别</text>
							<text class="option-desc">实时扫描车牌号码</text>
						</view>
					</view>

					<view class="option-item" @click="chooseFromAlbum">
						<view class="option-icon">
							<u-icon name="photo" size="40" color="#19be6b"></u-icon>
						</view>
						<view class="option-content">
							<text class="option-title">相册选择</text>
							<text class="option-desc">从相册选择车牌图片</text>
						</view>
					</view>
				</view>

				<!-- 识别结果显示 -->
				<view class="recognition-result" v-if="recognitionResult">
					<view class="result-header">
						<u-icon name="checkmark-circle" size="40" color="#19be6b"></u-icon>
						<text class="result-title">识别成功</text>
					</view>
					<view class="result-plate">
						<text class="plate-text">{{ recognitionResult.plateNumber }}</text>
						<text class="plate-color" v-if="recognitionResult.color">{{ recognitionResult.color }}</text>
						<text class="plate-confidence" v-if="recognitionResult.confidence">置信度: {{ recognitionResult.confidence }}%</text>
					</view>
					<view class="result-actions">
						<view class="action-btn use-btn" @click="useRecognitionResult">
							<text>使用此结果</text>
						</view>
						<view class="action-btn retry-btn" @click="retryRecognition">
							<text>重新识别</text>
						</view>
					</view>
				</view>
			</view>
		</u-modal>

	</view>
</template>

<script>
export default {
	data() {
		return {
			currentUserRole: 'manager',
			formData: {
				plateNumber: '',
				violationType: '',
				customType: '',
				location: '',
				photos: [],
				description: ''
			},
			ownerInfo: null,
			// 违规类型配置 - 基于真实数据和使用频率
			violationConfig: {
				// 常用类型（基于使用频率统计）
				common: [
					{ name: '超时停车', value: 'overtime', icon: '🚗', usage: 45 },
					{ name: '未按位停车', value: 'wrong_position', icon: '🅿️', usage: 30 },
					{ name: '占用他人车位', value: 'occupy_space', icon: '🚫', usage: 15 },
					{ name: '遮挡车牌', value: 'block_plate', icon: '🚫', usage: 12 },
					{ name: '堵塞消防通道', value: 'block_passage', icon: '🚧', usage: 10 },
					{ name: '压线停车', value: 'cross_line', icon: '📏', usage: 8 }
				],
				// 其他类型
				others: [
					{ name: '未经授权停车', value: 'unauthorized', icon: '🔒', usage: 8 },
					{ name: '占用残疾人车位', value: 'disabled_space', icon: '♿', usage: 4 },
					{ name: '逆向停车', value: 'reverse_parking', icon: '🔄', usage: 3 },
					{ name: '跨车位停车', value: 'cross_parking', icon: '📐', usage: 3 },
					{ name: '占用VIP车位', value: 'vip_space', icon: '👑', usage: 2 },
					{ name: '未熄火停车', value: 'engine_on', icon: '🔥', usage: 2 },
					{ name: '占用卸货区', value: 'loading_zone', icon: '📦', usage: 2 },
					{ name: '超宽停车', value: 'oversized', icon: '📏', usage: 1 },
					{ name: '占用绿化带', value: 'green_belt', icon: '🌱', usage: 1 },
					{ name: '占用充电桩车位', value: 'charging_space', icon: '🔌', usage: 1 },
					{ name: '车辆损坏', value: 'vehicle_damage', icon: '🔧', usage: 1 },
					{ name: '其他', value: 'other', icon: '➕', usage: 1 }
				]
			},

			// 常用位置 - 基于真实数据
			commonLocations: [
				'A区-15号车位', 'B区-08号车位', 'C区-22号车位', 'D区-05号车位',
				'E区-新能源专用位', 'A区-03号车位', 'B区-12号车位', 'C区-18号车位',
				'A区-10号车位', 'B区-16号车位', 'C区-11号车位', 'D区-09号车位',
				'A区-07号车位', 'B区-14号车位', 'C区-06号车位', 'D区-25号车位'
			],

			// 界面状态
			uiState: {
				showMoreTypes: false,    // 是否展开更多类型
				searchKeyword: '',       // 搜索关键词
				isSearching: false       // 是否在搜索状态
			},
			// 搜索相关
			searchTimer: null,           // 搜索防抖定时器
			showScanModal: false,
			scanResult: '',
			scanning: false,
			showConfirmModal: false,
			submitting: false,
			// 车牌识别相关
			showCamera: false,
			isRecognizing: false,
			debugMode: false,
			// 自动识别相关变量
			autoRecognize: false,        // 自动识别开关
			autoRecognizeTimer: null,    // 自动识别定时器
			autoRecognizeCount: 0,       // 自动识别次数计数
			autoRecognizeInterval: 2000, // 自动识别间隔（2秒）
			lastRecognizeTime: 0,        // 上次识别时间（防抖用）
			lastResult: null,            // 最新识别结果

			// 车牌搜索相关
			showPlateSuggestions: false,     // 是否显示车牌搜索建议
			plateSuggestions: [],            // 车牌搜索建议列表
			plateSearchTimer: null,          // 车牌搜索防抖定时器

			// 位置搜索相关
			showLocationSuggestions: false,  // 是否显示位置搜索建议
			locationSuggestions: [],         // 位置搜索建议列表
			locationSearchTimer: null,       // 位置搜索防抖定时器

			// 车牌识别相关
			showPlateRecognitionModal: false, // 是否显示车牌识别弹窗
			showCamera: false,                // 是否显示摄像头
			isRecognizing: false,             // 是否正在识别
			recognitionResult: null,          // 识别结果对象
			autoRecognize: false,             // 自动识别开关
			autoRecognizeTimer: null,         // 自动识别定时器
			autoRecognizeCount: 0,            // 自动识别次数
			lastRecognizeTime: 0,             // 上次识别时间
		}
	},

	// 添加组件销毁时的清理
	beforeDestroy() {
		this.stopAutoRecognize();
		// 清理搜索定时器
		if (this.searchTimer) {
			clearTimeout(this.searchTimer);
			this.searchTimer = null;
		}
		// 清理车牌搜索定时器
		if (this.plateSearchTimer) {
			clearTimeout(this.plateSearchTimer);
			this.plateSearchTimer = null;
		}
		// 清理位置搜索定时器
		if (this.locationSearchTimer) {
			clearTimeout(this.locationSearchTimer);
			this.locationSearchTimer = null;
		}
		// 清理自动识别定时器
		if (this.autoRecognizeTimer) {
			clearTimeout(this.autoRecognizeTimer);
			this.autoRecognizeTimer = null;
		}
	},

	computed: {
		canSubmit() {
			return this.formData.plateNumber &&
				   this.formData.violationType &&
				   this.formData.location &&
				   (this.formData.violationType !== 'other' || this.formData.customType);
		},

		// 根据违规类型动态显示相关描述模板
		descriptionTemplates() {
			const baseTemplates = [
				'车辆停放超出白线范围',
				'占用他人预约车位',
				'停车时间超过预约时长',
				'车辆停放角度不正确',
				'未按规定方向停车',
				'车辆阻挡通道'
			];

			const typeSpecificTemplates = {
				'overtime': [
					'停车时间超过2小时',
					'超过预约时长30分钟',
					'夜间停车超时'
				],
				'wrong_position': [
					'车辆停放超出白线范围',
					'车辆停放角度不正确',
					'未按规定方向停车'
				],
				'occupy_space': [
					'占用他人预约车位',
					'占用访客车位',
					'占用业主专用车位'
				],
				'block_plate': [
					'车牌被遮挡无法识别',
					'故意遮挡车牌号码'
				],
				'block_passage': [
					'车辆阻挡消防通道',
					'车辆阻挡行车道',
					'车辆阻挡人行通道'
				]
			};

			// 如果选择了特定类型，返回对应模板，否则返回基础模板
			if (this.formData.violationType && typeSpecificTemplates[this.formData.violationType]) {
				return typeSpecificTemplates[this.formData.violationType];
			}
			return baseTemplates;
		},

		// 计算信用分样式类
		creditScoreClass() {
			if (!this.ownerInfo || !this.ownerInfo.creditScore) {
				return '';
			}
			const score = this.ownerInfo.creditScore;
			if (score >= 80) return 'credit-excellent';
			if (score >= 60) return 'credit-warning';
			return 'credit-danger';
		},

		// 当前显示的常用类型（排除已选择的）
		displayCommonTypes() {
			return this.violationConfig.common.filter(type =>
				type.value !== this.formData.violationType
			);
		},

		// 搜索结果
		searchResults() {
			if (!this.uiState.searchKeyword) return [];

			const keyword = this.uiState.searchKeyword.toLowerCase();
			const allTypes = [...this.violationConfig.common, ...this.violationConfig.others];

			return allTypes.filter(type =>
				type.name.toLowerCase().includes(keyword) ||
				type.value.toLowerCase().includes(keyword)
			);
		},

		// 已选择的类型信息
		selectedTypeInfo() {
			if (!this.formData.violationType) return null;

			const allTypes = [...this.violationConfig.common, ...this.violationConfig.others];
			return allTypes.find(type => type.value === this.formData.violationType);
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},



		// 车牌号输入变化
		async onPlateNumberChange() {
			if (this.formData.plateNumber && this.formData.plateNumber.length >= 7) {
				try {
					this.ownerInfo = await this.getOwnerInfo(this.formData.plateNumber);
				} catch (error) {
					this.ownerInfo = null;
				}
			} else {
				this.ownerInfo = null;
			}
		},

		// 获取车主信息 - 基于真实数据
		async getOwnerInfo(plateNumber) {
			return new Promise((resolve) => {
				setTimeout(() => {
					const realOwnerData = {
						'黑A12345': {
							name: '张三',
							phone: '138****5678',
							address: '8栋5单元555室',
							creditScore: 85
						},
						'黑A34567': {
							name: '张三',
							phone: '138****5678',
							address: '8栋5单元555室',
							creditScore: 85
						},
						'黑A45678': {
							name: '张三',
							phone: '138****5678',
							address: '8栋5单元555室',
							creditScore: 85
						},
						'黑B67890': {
							name: '黄巢',
							phone: '137****4478',
							address: '5栋1单元105室',
							creditScore: 92
						},
						'黑A01234': {
							name: '萧燕燕',
							phone: '138****4478',
							address: '2栋7单元105室',
							creditScore: 78
						},
						'黑A24680': {
							name: '赵光义',
							phone: '138****4478',
							address: '25栋7单元155室',
							creditScore: 88
						},
						'黑AF57913': {
							name: '王小川',
							phone: '135****8574',
							address: '15栋4单元707室',
							creditScore: 72
						},
						'黑AD98765': {
							name: '李明',
							phone: '139****2345',
							address: '3栋2单元201室',
							creditScore: 90
						},
						'黑AD12345': {
							name: '王芳',
							phone: '136****7890',
							address: '6栋3单元302室',
							creditScore: 83
						},
						'黑AD7890': {
							name: '刘强',
							phone: '135****6789',
							address: '9栋1单元101室',
							creditScore: 76
						}
					};
					resolve(realOwnerData[plateNumber] || null);
				}, 500);
			});
		},

		// ================ 车牌搜索框相关方法 ================

		// 车牌号输入事件
		onPlateNumberInput(e) {
			const value = e.detail.value || e.target.value || '';
			this.formData.plateNumber = value.toUpperCase(); // 转换为大写

			// 清除之前的定时器
			if (this.plateSearchTimer) {
				clearTimeout(this.plateSearchTimer);
			}

			// 防抖搜索
			this.plateSearchTimer = setTimeout(() => {
				this.generatePlateSuggestions(value);
				this.onPlateNumberChange(); // 查询车主信息
			}, 300);
		},

		// 车牌搜索框获得焦点
		onPlateSearchFocus() {
			if (this.formData.plateNumber) {
				this.generatePlateSuggestions(this.formData.plateNumber);
			}
		},

		// 车牌搜索框失去焦点
		onPlateSearchBlur() {
			// 延迟隐藏建议，以便用户可以点击建议项
			setTimeout(() => {
				this.showPlateSuggestions = false;
			}, 200);
		},

		// 清空车牌号
		clearPlateNumber() {
			this.formData.plateNumber = '';
			this.showPlateSuggestions = false;
			this.ownerInfo = null;
		},

		// 生成车牌搜索建议 - 基于真实数据
		generatePlateSuggestions(keyword) {
			if (!keyword || keyword.length < 2) {
				this.showPlateSuggestions = false;
				return;
			}

			// 真实车牌数据库
			const realPlateData = [
				{ plateNumber: '黑A12345', ownerName: '张三' },
				{ plateNumber: '黑A34567', ownerName: '张三' },
				{ plateNumber: '黑A45678', ownerName: '张三' },
				{ plateNumber: '黑B67890', ownerName: '黄巢' },
				{ plateNumber: '黑A01234', ownerName: '萧燕燕' },
				{ plateNumber: '黑A24680', ownerName: '赵光义' },
				{ plateNumber: '黑AF57913', ownerName: '王小川' },
				{ plateNumber: '黑AD98765', ownerName: '李明' },
				{ plateNumber: '黑AD12345', ownerName: '王芳' },
				{ plateNumber: '黑AD7890', ownerName: '刘强' },
				{ plateNumber: '黑A54321', ownerName: '赵六' },
				{ plateNumber: '黑B98765', ownerName: '钱七' },
				{ plateNumber: '黑C11111', ownerName: '孙八' }
			];

			// 过滤匹配的车牌
			const suggestions = realPlateData.filter(item =>
				item.plateNumber.toLowerCase().includes(keyword.toLowerCase())
			).slice(0, 5); // 最多显示5个建议

			this.plateSuggestions = suggestions;
			this.showPlateSuggestions = suggestions.length > 0;
		},

		// 选择车牌建议
		selectPlateSuggestion(suggestion) {
			this.formData.plateNumber = suggestion.plateNumber;
			this.showPlateSuggestions = false;
			this.onPlateNumberChange(); // 查询车主信息
		},

		// ================ 车牌识别相关方法 ================

		// 打开车牌识别
		openPlateRecognition() {
			console.log('🎯 [车牌识别] 打开车牌识别弹窗');
			this.showPlateRecognitionModal = true;
			this.showCamera = false;
			this.recognitionResult = null;
			this.isRecognizing = false;
			this.stopAutoRecognize();
		},

		// 关闭车牌识别
		closePlateRecognition() {
			this.showPlateRecognitionModal = false;
			this.showCamera = false;
			this.recognitionResult = null;
			this.isRecognizing = false;
			this.stopAutoRecognize();
		},

		// 打开摄像头
		openCamera() {
			this.showCamera = true;
			// 延迟启动自动识别，等待摄像头初始化
			setTimeout(() => {
				this.startAutoRecognize();
			}, 1000);
		},

		// 关闭摄像头
		closeCamera() {
			this.stopAutoRecognize();
			this.showCamera = false;
		},

		// 开启自动识别
		startAutoRecognize() {
			if (this.autoRecognize) return;

			this.autoRecognize = true;
			this.autoRecognizeCount = 0;
			console.log('🚀 开启自动识别模式');

			// 立即开始第一次识别
			this.performAutoRecognize();
		},

		// 停止自动识别
		stopAutoRecognize() {
			if (!this.autoRecognize) return;

			this.autoRecognize = false;
			if (this.autoRecognizeTimer) {
				clearTimeout(this.autoRecognizeTimer);
				this.autoRecognizeTimer = null;
			}
			console.log('⏹️ 停止自动识别模式');
		},

		// 切换自动识别
		toggleAutoRecognize() {
			if (this.autoRecognize) {
				this.stopAutoRecognize();
			} else {
				this.startAutoRecognize();
			}
		},

		// 执行自动识别
		async performAutoRecognize() {
			if (!this.autoRecognize || !this.showCamera) return;

			// 防抖：如果正在识别中，跳过此次
			if (this.isRecognizing) {
				console.log('⏭️ 跳过自动识别（正在识别中）');
				this.scheduleNextAutoRecognize();
				return;
			}

			// 防抖：检查距离上次识别的时间间隔
			const now = Date.now();
			if (now - this.lastRecognizeTime < 2000) {
				console.log('⏭️ 跳过自动识别（间隔太短）');
				this.scheduleNextAutoRecognize();
				return;
			}

			this.autoRecognizeCount++;
			console.log(`🔄 执行第${this.autoRecognizeCount}次自动识别`);

			try {
				await this.takePhotoAndRecognize(true); // true表示自动识别
			} catch (error) {
				console.error('自动识别失败:', error);
			}

			// 安排下次自动识别
			this.scheduleNextAutoRecognize();
		},

		// 安排下次自动识别
		scheduleNextAutoRecognize() {
			if (!this.autoRecognize) return;

			this.autoRecognizeTimer = setTimeout(() => {
				this.performAutoRecognize();
			}, 3000); // 3秒间隔
		},

		// 拍照并识别（自动）
		async takePhotoAndRecognize(isAutoRecognition = false) {
			if (this.isRecognizing) return;

			this.isRecognizing = true;
			this.lastRecognizeTime = Date.now();

			return new Promise((resolve, reject) => {
				try {
					// 从camera组件获取照片
					const ctx = uni.createCameraContext('camera', this);
					ctx.takePhoto({
						quality: 'high',
						success: async (res) => {
							try {
								await this.recognizeFromImage(res.tempImagePath, isAutoRecognition);
								resolve();
							} catch (error) {
								reject(error);
							}
						},
						fail: (err) => {
							console.error('自动拍照失败:', err);
							this.isRecognizing = false;
							reject(err);
						}
					});
				} catch (error) {
					console.error('自动拍照异常:', error);
					this.isRecognizing = false;
					reject(error);
				}
			});
		},

		// 手动拍照识别
		async capturePhoto() {
			if (this.isRecognizing || this.autoRecognize) return;

			this.isRecognizing = true;
			try {
				// 从camera组件获取照片
				const ctx = uni.createCameraContext('camera', this);
				ctx.takePhoto({
					quality: 'high',
					success: (res) => {
						this.recognizeFromImage(res.tempImagePath, false); // false表示手动识别
					},
					fail: (err) => {
						console.error('拍照失败:', err);
						uni.showToast({
							title: '拍照失败',
							icon: 'none'
						});
						this.isRecognizing = false;
					}
				});
			} catch (error) {
				console.error('拍照异常:', error);
				this.isRecognizing = false;
				uni.showToast({
					title: '拍照失败',
					icon: 'none'
				});
			}
		},

		// 摄像头错误处理
		handleCameraError(error) {
			console.error('摄像头错误:', error);
			uni.showToast({
				title: '摄像头启动失败',
				icon: 'none'
			});
			this.showCamera = false;
		},

		// 从相册选择图片
		chooseFromAlbum() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album'],
				success: (res) => {
					this.recognizeFromImage(res.tempFilePaths[0]);
				}
			});
		},

		// 从图片识别车牌
		async recognizeFromImage(imagePath, isAutoRecognition = false) {
			if (!isAutoRecognition) {
				uni.showLoading({ title: '识别中...' });
			}

			try {
				const base64 = await this.imageToBase64(imagePath);
				const result = await this.callRecognitionAPI(base64, isAutoRecognition);

				if (result && result.success) {
					this.recognitionResult = {
						plateNumber: result.plateNumber || 'Unknown',
						color: result.color || '未知',
						confidence: result.confidence || 0,
						recognizeTime: new Date().toISOString()
					};

					if (isAutoRecognition) {
						// 自动识别成功，用提示框显示车牌号码
						console.log(`✅ 自动识别成功: ${result.plateNumber}`);
						uni.showModal({
							title: '车牌识别成功',
							content: `识别到车牌号码：${result.plateNumber}\n车牌颜色：${result.color}\n置信度：${result.confidence}%`,
							confirmText: '继续识别',
							cancelText: '停止',
							success: (res) => {
								if (res.cancel) {
									// 用户选择停止，关闭摄像头
									this.stopAutoRecognize();
									this.showCamera = false;
								}
								// 如果选择继续，自动识别会继续运行
							}
						});
					} else {
						// 手动识别成功，关闭摄像头并显示结果
						this.showCamera = false;
						this.stopAutoRecognize();
						console.log(`✅ 手动识别成功: ${result.plateNumber}`);
					}
				} else {
					// 识别失败
					if (!isAutoRecognition) {
						uni.showToast({
							title: result?.message || '识别失败，请重试',
							icon: 'none'
						});
					}
					console.log('❌ 识别失败:', result?.message || '未知错误');
				}
			} catch (error) {
				console.error('识别异常:', error);
				if (!isAutoRecognition) {
					uni.showToast({
						title: '识别失败，请重试',
						icon: 'none'
					});
				}
			} finally {
				if (!isAutoRecognition) {
					uni.hideLoading();
				}
				this.isRecognizing = false;
			}
		},

		// 调用识别API
		async callRecognitionAPI(base64Image, isAutoRecognition = false) {
			console.log(isAutoRecognition ? '🤖 自动识别API调用' : '👆 手动识别API调用');

			try {
				// 移除base64中的空白字符
				const cleanBase64 = base64Image.replace(/\s/g, '');

				const response = await uni.request({
					url: 'http://localhost:8543/api/plate/recognize',
					method: 'POST',
					header: {
						'Content-Type': 'application/json'
					},
					data: {
						image: cleanBase64,
						multiDetect: false
					},
					timeout: isAutoRecognition ? 15000 : 30000  // 自动识别使用较短超时
				});

				console.log('API响应状态:', response.statusCode);

				if (response.statusCode === 200 && response.data) {
					const data = response.data;
					console.log('API响应数据:', data);

					if (data.success && data.data && data.data.length > 0) {
						const plateData = data.data[0];
						return {
							success: true,
							plateNumber: plateData.number || plateData.plateNumber,
							color: plateData.color,
							confidence: Math.round(plateData.probability * 100)
						};
					} else {
						return {
							success: false,
							message: data.message || '未识别到车牌'
						};
					}
				} else {
					return {
						success: false,
						message: `API调用失败: ${response.statusCode}`
					};
				}
			} catch (error) {
				console.error('API调用异常:', error);
				return {
					success: false,
					message: '网络请求失败'
				};
			}
		},

		// 图片转base64
		imageToBase64(imagePath) {
			return new Promise((resolve, reject) => {
				uni.getFileSystemManager().readFile({
					filePath: imagePath,
					encoding: 'base64',
					success: (res) => {
						// 确保base64数据格式正确，移除可能的换行符和空格
						let base64Data = res.data;
						if (base64Data) {
							base64Data = base64Data.replace(/\s/g, ''); // 移除所有空白字符
							console.log('Base64 图片大小:', base64Data.length);
						}
						resolve(base64Data);
					},
					fail: (error) => {
						console.error('图片转base64失败:', error);
						reject(error);
					}
				});
			});
		},

		// 使用识别结果
		useRecognitionResult() {
			if (this.recognitionResult && this.recognitionResult.plateNumber) {
				this.formData.plateNumber = this.recognitionResult.plateNumber;
				this.closePlateRecognition();
				this.onPlateNumberChange(); // 查询车主信息

				uni.showToast({
					title: '已使用识别结果',
					icon: 'success'
				});
			}
		},

		// 重新识别
		retryRecognition() {
			this.recognitionResult = null;
			this.showCamera = false;
		},

		// 选择违规类型（新的统一方法）
		selectType(type) {
			this.formData.violationType = type.value;
			if (type.value !== 'other') {
				this.formData.customType = '';
			}

			// 选择后收起展开状态和搜索状态
			this.uiState.showMoreTypes = false;
			this.uiState.isSearching = false;
			this.uiState.searchKeyword = '';

			// 自动填充建议描述（如果当前描述为空）
			if (!this.formData.description) {
				this.autoFillDescription(type.value);
			}
		},

		// 自动填充描述
		autoFillDescription(violationType) {
			const autoDescriptions = {
				'overtime': '车辆停车时间超过规定时长',
				'wrong_position': '车辆未按规定位置停放',
				'occupy_space': '车辆占用他人预约车位',
				'block_plate': '车牌被遮挡，无法正常识别',
				'block_passage': '车辆阻挡消防通道或行车道',
				'unauthorized': '车辆未经授权在此区域停车',
				'disabled_space': '车辆占用残疾人专用车位',
				'cross_line': '车辆停放时压线或超出车位范围',
				'cross_parking': '车辆跨越多个车位停放',
				'vip_space': '车辆占用VIP专用车位',
				'engine_on': '车辆停车时未熄火',
				'loading_zone': '车辆占用货物装卸区域',
				'oversized': '车辆超出标准车位尺寸',
				'green_belt': '车辆占用绿化带区域',
				'charging_space': '车辆占用电动车充电专用车位',
				'vehicle_damage': '车辆存在损坏影响停车秩序'
			};

			if (autoDescriptions[violationType]) {
				this.formData.description = autoDescriptions[violationType];
			}
		},

		// 切换更多类型显示
		toggleMoreTypes() {
			this.uiState.showMoreTypes = !this.uiState.showMoreTypes;
			if (!this.uiState.showMoreTypes) {
				this.uiState.isSearching = false;
				this.uiState.searchKeyword = '';
			}
		},

		// 开始搜索
		startSearch() {
			this.uiState.isSearching = true;
			this.uiState.showMoreTypes = true;
		},

		// 搜索输入处理（带防抖）
		onSearchInput() {
			// 清除之前的定时器
			if (this.searchTimer) {
				clearTimeout(this.searchTimer);
			}

			// 设置新的防抖定时器
			this.searchTimer = setTimeout(() => {
				// 如果搜索关键词为空，显示提示
				if (!this.uiState.searchKeyword.trim()) {
					console.log('🔍 搜索关键词为空');
					return;
				}

				// 执行搜索逻辑
				console.log('🔍 搜索关键词:', this.uiState.searchKeyword);

				// 如果没有搜索结果，可以显示提示
				if (this.searchResults.length === 0) {
					console.log('🔍 没有找到匹配的违规类型');
				}
			}, 300); // 300ms 防抖延迟
		},

		// 清空搜索
		clearSearch() {
			this.uiState.searchKeyword = '';
			if (this.searchTimer) {
				clearTimeout(this.searchTimer);
				this.searchTimer = null;
			}
		},

		// 取消搜索
		cancelSearch() {
			this.clearSearch();
			this.uiState.isSearching = false;
		},

		// ================ 位置输入相关方法 ================

		// 位置输入事件
		onLocationInput(e) {
			const value = e.detail.value || e.target.value || '';
			this.formData.location = value;

			// 清除之前的定时器
			if (this.locationSearchTimer) {
				clearTimeout(this.locationSearchTimer);
			}

			// 防抖搜索
			this.locationSearchTimer = setTimeout(() => {
				this.generateLocationSuggestions(value);
			}, 300);
		},

		// 位置输入框获得焦点
		onLocationFocus() {
			if (this.formData.location) {
				this.generateLocationSuggestions(this.formData.location);
			}
		},

		// 位置输入框失去焦点
		onLocationBlur() {
			// 延迟隐藏建议，以便用户可以点击建议项
			setTimeout(() => {
				this.showLocationSuggestions = false;
			}, 200);
		},

		// 生成位置搜索建议
		generateLocationSuggestions(keyword) {
			if (!keyword || keyword.length < 1) {
				this.showLocationSuggestions = false;
				return;
			}

			// 过滤匹配的位置
			const suggestions = this.commonLocations.filter(location =>
				location.toLowerCase().includes(keyword.toLowerCase())
			).slice(0, 5); // 最多显示5个建议

			this.locationSuggestions = suggestions;
			this.showLocationSuggestions = suggestions.length > 0;
		},

		// 选择位置建议
		selectLocationSuggestion(location) {
			this.formData.location = location;
			this.showLocationSuggestions = false;
		},

		// ================ 描述模板相关方法 ================

		// 使用描述模板
		useDescriptionTemplate(template) {
			if (this.formData.description) {
				// 如果已有描述，追加模板
				this.formData.description += (this.formData.description.endsWith('。') ? '' : '，') + template;
			} else {
				// 如果没有描述，直接使用模板
				this.formData.description = template;
			}
		},

		// 获取违规类型名称
		getViolationTypeName() {
			if (this.formData.violationType === 'other') {
				return this.formData.customType || '其他';
			}
			const type = this.violationTypes.find(t => t.value === this.formData.violationType);
			return type ? type.name : '';
		},

		// 获取当前位置
		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					// 从常用位置中随机选择一个
					const randomIndex = Math.floor(Math.random() * this.commonLocations.length);
					this.formData.location = this.commonLocations[randomIndex];
					uni.showToast({
						title: '定位成功',
						icon: 'success'
					});
				},
				fail: () => {
					// 定位失败时也从常用位置中选择
					const randomIndex = Math.floor(Math.random() * this.commonLocations.length);
					this.formData.location = this.commonLocations[randomIndex];
					uni.showToast({
						title: '已选择常用位置',
						icon: 'none'
					});
				}
			});
		},

		// 拍照
		takePhoto() {
			uni.chooseImage({
				count: 6 - this.formData.photos.length,
				sizeType: ['compressed'],
				sourceType: ['camera'],
				success: (res) => {
					this.formData.photos.push(...res.tempFilePaths);
				}
			});
		},

		// 预览照片
		previewPhoto(index) {
			uni.previewImage({
				urls: this.formData.photos,
				current: index
			});
		},

		// 删除照片
		deletePhoto(index) {
			this.formData.photos.splice(index, 1);
		},



		// 播放语音
		playVoice() {
			uni.showToast({
				title: '播放语音',
				icon: 'none'
			});
		},

		// 删除语音
		deleteVoice() {
			this.formData.voiceMemo = null;
			this.voiceDuration = 0;
		},

		// 打开车牌扫描
		openPlateScanner() {
			this.showScanModal = true;
			this.scanResult = '';
			this.showCamera = false;
		},

		// 关闭车牌扫描
		closeScanModal() {
			this.stopAutoRecognize();
			this.showScanModal = false;
			this.showCamera = false;
			this.scanning = false;
			this.scanResult = '';
		},

		// 启动摄像头
		startCamera() {
			this.showCamera = true;
			// 自动开始识别
			setTimeout(() => {
				this.startAutoRecognize();
			}, 1000);
		},

		// 关闭摄像头
		closeCamera() {
			this.stopAutoRecognize();
			this.showCamera = false;
		},

		// 开启自动识别
		startAutoRecognize() {
			if (this.autoRecognize) return;

			this.autoRecognize = true;
			this.autoRecognizeCount = 0;
			console.log('🚀 开启自动识别模式');

			// 立即开始第一次识别
			this.performAutoRecognize();
		},

		// 停止自动识别
		stopAutoRecognize() {
			if (!this.autoRecognize) return;

			this.autoRecognize = false;
			if (this.autoRecognizeTimer) {
				clearTimeout(this.autoRecognizeTimer);
				this.autoRecognizeTimer = null;
			}
			console.log('⏹️ 停止自动识别模式');
		},

		// 使用扫描结果
		useScanResult() {
			this.formData.plateNumber = this.scanResult;
			this.onPlateNumberChange();
			this.closeScanModal();
		},

		// 提交违规记录
		submitViolation() {
			if (!this.canSubmit) {
				uni.showToast({
					title: '请完善必填信息',
					icon: 'none'
				});
				return;
			}
			this.showConfirmModal = true;
		},

		// 关闭确认弹窗
		closeConfirmModal() {
			this.showConfirmModal = false;
		},

		// 确认提交
		async confirmSubmit() {
			this.submitting = true;
			try {
				const result = await this.submitToServer();

				// 显示详细的成功信息
				uni.showModal({
					title: '提交成功',
					content: `违规记录已成功提交\n记录编号：${result.violationId}\n信用分影响：${this.calculateCreditImpact()}分`,
					showCancel: false,
					confirmText: '确定',
					success: () => {
						setTimeout(() => {
							uni.navigateBack();
						}, 500);
					}
				});
			} catch (error) {
				uni.showModal({
					title: '提交失败',
					content: error.message || '网络错误，请检查网络连接后重试',
					showCancel: true,
					cancelText: '取消',
					confirmText: '重试',
					success: (res) => {
						if (res.confirm) {
							// 用户选择重试
							setTimeout(() => {
								this.confirmSubmit();
							}, 500);
						}
					}
				});
			} finally {
				this.submitting = false;
				this.closeConfirmModal();
			}
		},

		// 提交到服务器
		async submitToServer() {
			return new Promise((resolve, reject) => {
				// 模拟真实的提交数据
				const submitData = {
					id: 'VIO' + Date.now(),
					plateNumber: this.formData.plateNumber,
					violationType: this.formData.violationType,
					customType: this.formData.customType,
					location: this.formData.location,
					description: this.formData.description,
					images: this.formData.images,
					voiceNote: this.formData.voiceNote,
					ownerInfo: this.ownerInfo,
					reportTime: new Date().toISOString(),
					reporterId: 'ADMIN001', // 管理员ID
					status: 'pending', // 待处理
					severity: this.calculateSeverity(), // 计算严重程度
					creditImpact: this.calculateCreditImpact() // 计算信用分影响
				};

				console.log('📤 提交违规数据:', submitData);

				// 模拟网络延迟和可能的失败
				setTimeout(() => {
					// 90% 成功率
					if (Math.random() > 0.1) {
						resolve({
							success: true,
							violationId: submitData.id,
							message: '违规记录已成功提交'
						});
					} else {
						reject(new Error('网络错误，请重试'));
					}
				}, 1500 + Math.random() * 1000); // 1.5-2.5秒延迟
			});
		},

		// 计算违规严重程度
		calculateSeverity() {
			const severityMap = {
				'block_passage': 'high',     // 堵塞通道 - 高
				'disabled_space': 'high',    // 占用残疾人车位 - 高
				'fire_lane': 'high',         // 占用消防通道 - 高
				'occupy_space': 'medium',    // 占用他人车位 - 中
				'unauthorized': 'medium',    // 未经授权停车 - 中
				'overtime': 'low',           // 超时停车 - 低
				'wrong_position': 'low',     // 未按位停车 - 低
				'block_plate': 'medium'      // 遮挡车牌 - 中
			};
			return severityMap[this.formData.violationType] || 'low';
		},

		// 计算信用分影响
		calculateCreditImpact() {
			const impactMap = {
				'high': -10,     // 高严重程度扣10分
				'medium': -5,    // 中等严重程度扣5分
				'low': -2        // 低严重程度扣2分
			};
			const severity = this.calculateSeverity();
			return impactMap[severity] || -2;
		},

		// 执行自动识别
		async performAutoRecognize() {
			if (!this.autoRecognize || !this.showCamera) return;

			// 防抖：如果正在识别中，跳过此次
			if (this.isRecognizing) {
				console.log('⏭️ 跳过自动识别（正在识别中）');
				this.scheduleNextAutoRecognize();
				return;
			}

			// 防抖：检查距离上次识别的时间间隔
			const now = Date.now();
			if (now - this.lastRecognizeTime < 2000) {
				console.log('⏭️ 跳过自动识别（间隔太短）');
				this.scheduleNextAutoRecognize();
				return;
			}

			this.autoRecognizeCount++;
			this.lastRecognizeTime = now;
			console.log(`🔍 执行第${this.autoRecognizeCount}次自动识别`);

			try {
				// 自动拍照
				await this.autoCapture();
			} catch (error) {
				console.error('自动识别失败:', error);
			}

			// 调度下次自动识别
			this.scheduleNextAutoRecognize();
		},

		// 调度下次自动识别
		scheduleNextAutoRecognize() {
			if (!this.autoRecognize) return;

			this.autoRecognizeTimer = setTimeout(() => {
				this.performAutoRecognize();
			}, this.autoRecognizeInterval);
		},

		// 自动拍照（用于自动识别）
		async autoCapture() {
			if (this.isRecognizing) return;

			this.isRecognizing = true;
			try {
				const ctx = uni.createCameraContext('camera', this);
				return new Promise((resolve, reject) => {
					ctx.takePhoto({
						quality: 'high',
						success: async (res) => {
							try {
								await this.recognizeFromImage(res.tempImagePath, true); // 传入true表示自动识别
								resolve();
							} catch (error) {
								reject(error);
							}
						},
						fail: (err) => {
							console.error('自动拍照失败:', err);
							this.isRecognizing = false;
							reject(err);
						}
					});
				});
			} catch (error) {
				console.error('自动拍照异常:', error);
				this.isRecognizing = false;
				throw error;
			}
		},

		// 拍照识别（手动）
		async capturePhoto() {
			if (this.isRecognizing || this.autoRecognize) return;

			this.isRecognizing = true;
			try {
				// 从camera组件获取照片
				const ctx = uni.createCameraContext('camera', this);
				ctx.takePhoto({
					quality: 'high',
					success: (res) => {
						this.recognizeFromImage(res.tempImagePath, false); // 传入false表示手动识别
					},
					fail: (err) => {
						console.error('拍照失败:', err);
						uni.showToast({
							title: '拍照失败',
							icon: 'none'
						});
						this.isRecognizing = false;
					}
				});
			} catch (error) {
				console.error('拍照异常:', error);
				this.isRecognizing = false;
				uni.showToast({
					title: '拍照失败',
					icon: 'none'
				});
			}
		},

		// 摄像头错误处理
		handleCameraError(error) {
			console.error('摄像头错误:', error);
			uni.showToast({
				title: '摄像头启动失败',
				icon: 'none'
			});
			this.showCamera = false;
		},

		// 从相册选择
		chooseFromAlbum() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album'],
				success: (res) => {
					this.recognizeFromImage(res.tempFilePaths[0]);
				}
			});
		},

		// 从图片识别
		async recognizeFromImage(imagePath, isAutoRecognition = false) {
			if (!isAutoRecognition) {
				uni.showLoading({ title: '识别中...' });
			}

			try {
				const base64 = await this.imageToBase64(imagePath);
				const result = await this.callRecognitionAPI(base64, isAutoRecognition);

				if (result && result.success) {
					this.lastResult = {
						plateNumber: result.plateNumber || 'Unknown',
						color: result.color || '未知',
						confidence: result.confidence || 0,
						recognizeTime: new Date().toISOString()
					};

					this.scanResult = result.plateNumber;

					if (isAutoRecognition) {
						// 自动识别成功，用提示框显示车牌号码
						console.log(`✅ 自动识别成功: ${result.plateNumber}`);
						uni.showModal({
							title: '车牌识别成功',
							content: `识别到车牌号码：${result.plateNumber}\n车牌颜色：${result.color}\n置信度：${result.confidence}%`,
							confirmText: '继续识别',
							cancelText: '停止',
							success: (res) => {
								if (res.cancel) {
									// 用户选择停止，关闭摄像头
									this.stopAutoRecognize();
									this.showCamera = false;
								}
								// 如果选择继续，自动识别会继续运行
							}
						});
					} else {
						// 手动识别成功，关闭摄像头并显示结果
						this.showCamera = false;
						this.stopAutoRecognize();

						uni.showToast({
							title: '识别成功',
							icon: 'success'
						});
					}
				} else {
					console.error('识别失败，API响应:', result);

					if (isAutoRecognition) {
						// 自动识别失败，静默处理，继续下次识别
						console.log(`❌ 第${this.autoRecognizeCount}次自动识别失败，继续下次识别`);
					} else {
						// 手动识别失败，显示错误信息
						const errorMsg = result && result.errorMessage ? result.errorMessage : '识别失败，请重试';
						uni.showToast({
							title: errorMsg,
							icon: 'none',
							duration: 3000
						});
					}
				}
			} catch (error) {
				console.error('识别API调用失败:', error);

				if (isAutoRecognition) {
					// 自动识别异常，静默处理
					console.log(`❌ 第${this.autoRecognizeCount}次自动识别异常:`, error.message);
				} else {
					// 手动识别异常，模拟结果用于测试
					this.lastResult = {
						plateNumber: '黑A12345',
						color: '蓝牌',
						confidence: 85,
						recognizeTime: new Date().toISOString()
					};

					this.scanResult = '黑A12345';
					this.showCamera = false;
					this.stopAutoRecognize();

					uni.showToast({
						title: '模拟识别成功（请配置API）',
						icon: 'success'
					});
				}
			} finally {
				if (!isAutoRecognition) {
					uni.hideLoading();
				}
				this.isRecognizing = false;
			}
		},

		// 调用识别API
		async callRecognitionAPI(base64Image, isAutoRecognition = false) {
			console.log(isAutoRecognition ? '🤖 自动识别API调用' : '👆 手动识别API调用');

			try {
				// 移除base64中的空白字符
				const cleanBase64 = base64Image.replace(/\s/g, '');

				const response = await uni.request({
					url: 'http://localhost:8543/api/plate/recognize',
					method: 'POST',
					header: {
						'Content-Type': 'application/json'
					},
					data: {
						image: cleanBase64,
						multiDetect: false
					},
					timeout: isAutoRecognition ? 15000 : 30000  // 自动识别使用较短超时
				});

				if (!isAutoRecognition) {
					console.log('车牌识别响应:', response);
				}

				if (response.statusCode === 200) {
					const result = response.data;

					// 检查响应格式：处理可能的双重嵌套结构
					let plateData = null;

					if (result.code === "0" && result.data) {
						// 检查是否是双重嵌套的Result结构
						if (result.data.code === "0" && result.data.data) {
							plateData = result.data.data; // 双重嵌套情况
						} else if (result.data.success) {
							plateData = result.data; // 正常情况
						}
					}

					if (plateData && plateData.success) {
						const recognitionResult = {
							success: true,
							plateNumber: plateData.plateNumber || '未识别',
							color: plateData.color || '未知',
							confidence: plateData.confidence || 0,
							recognizeTime: new Date().toISOString()
						};

						return recognitionResult;
					} else {
						// 处理错误情况
						let errorMsg = '识别失败';

						// 更智能的错误信息提取
						if (result.code === "-1") {
							errorMsg = result.msg || '识别失败';
						} else if (result.data && result.data.code === "-1") {
							errorMsg = result.data.msg || '识别失败';
						} else if (result.data && result.data.errorMessage) {
							errorMsg = result.data.errorMessage;
						} else if (!plateData) {
							errorMsg = '未检测到车牌或响应格式异常';
						}

						throw new Error(errorMsg);
					}
				} else {
					throw new Error(`请求失败: ${response.statusCode}`);
				}
			} catch (error) {
				if (!isAutoRecognition) {
					console.error('车牌识别失败:', error);
					uni.showToast({
						title: error.message || '识别失败',
						icon: 'error',
						duration: 3000
					});
				}

				// 返回错误结果
				return {
					success: false,
					errorMessage: error.message || '识别失败'
				};
			} finally {
				this.isRecognizing = false;
			}
		},

		// 图片转base64
		imageToBase64(imagePath) {
			return new Promise((resolve, reject) => {
				uni.getFileSystemManager().readFile({
					filePath: imagePath,
					encoding: 'base64',
					success: (res) => {
						// 确保base64数据格式正确，移除可能的换行符和空格
						let base64Data = res.data;
						if (base64Data) {
							base64Data = base64Data.replace(/\s/g, ''); // 移除所有空白字符
							console.log('Base64 图片大小:', base64Data.length);
						}
						resolve(base64Data);
					},
					fail: (error) => {
						console.error('图片转base64失败:', error);
						reject(error);
					}
				});
			});
		},

		// ================ 车牌键盘相关方法 ================

		// 显示键盘并传递当前车牌号码
		showKeyboardWithCurrentValue() {
			// 获取当前输入的车牌号码
			const currentPlateNumber = this.getPlateNumber();
			console.log('🎹 显示键盘，当前车牌号码:', currentPlateNumber);
			this.toShow(currentPlateNumber);
		},

		// 获取当前车牌号码
		getPlateNumber() {
			return this.formData.plateNumber || '';
		},

		toShow(value) {
			this.value = value || '';
			this.isShow = true;
			this.$refs.keyboardInput.changeValue(this.value);
			this.$nextTick(() => {
				this.updateCurrentPlateChars();
			});
		},

		keyboardClosed() {
			this.isShow = false;
			this.clearCurrentPlateChars();
		},

		toCancel() {
			this.keyboardClosed();
		},

		toConfirm() {
			this.isShow = false;
			if (this.$refs.keyboardInput && this.$refs.keyboardInput.values) {
				let value = this.$refs.keyboardInput.values.join('');
				this.formData.plateNumber = value;
				this.onPlateNumberChange();
			}
		},

		inputChange(index) {
			this.carIndex = index;

			// 安全检查：确保 keyboardInput 组件存在且有 values 属性
			if (!this.$refs.keyboardInput || !this.$refs.keyboardInput.values) {
				console.warn('keyboardInput 组件或其 values 属性不存在');
				return;
			}

			let newValue = this.$refs.keyboardInput.values[index - 1];

			// 安全检查：确保 keyboardBox 组件存在
			if (!this.$refs.keyboardBox) {
				console.warn('keyboardBox 组件不存在');
				return;
			}

			if (index == 0) {
				this.$refs.keyboardBox.changeMode(index == 0 ? 0 : 1);
			} else {
				this.$refs.keyboardBox.changeMode(1);
			}

			// 更新车牌字符数组以保持同步
			this.$nextTick(() => {
				this.updateCurrentPlateChars();
			});
		},

		inputAdd(v) {
			console.log('➕ 执行添加操作:', v);
			this.$refs.keyboardInput.toAdd(v);
			this.$nextTick(() => {
				console.log('🔄 添加后更新车牌字符数组');
				this.updateCurrentPlateChars();
				// 触发计算属性更新
				this.plateUpdateTrigger++;
			});
		},

		inputDel() {
			console.log('🗑️ 执行删除操作');
			this.$refs.keyboardInput.toDel();

			// 立即更新预览区域，确保删除操作能实时反映
			this.$nextTick(() => {
				console.log('🔄 删除后更新车牌字符数组');
				this.updateCurrentPlateChars();
				// 触发计算属性更新
				this.plateUpdateTrigger++;

				// 添加额外的延迟确保更新
				setTimeout(() => {
					this.$forceUpdate();
					console.log('🔄 强制更新完成');
				}, 50);
			});
		},

		inputClear() {
			console.log('🧹 执行清除操作');
			this.$refs.keyboardInput.toClear();
			this.clearCurrentPlateChars();
			// 触发计算属性更新
			this.plateUpdateTrigger++;
		},

		// 车牌颜色切换
		changeColor(color) {
			console.log('🎨 切换车牌类型，清除之前输入的车牌号码');

			// 先清除所有输入的车牌号码
			this.clearAllPlateInput();

			this.carColor = color;
			this.selectedColor = color;

			if (color == 'linear-gradient(to bottom, #d0f1e4, #6ad390)') {
				this.carMax = false;
				this.maxCarLenght = 8;
				this.plateType = "newEnergy";
				this.borderBgColor = "#000";
				this.dynamicWidth = 22;
				this.textColor = '#000';
			} else {
				this.carMax = true;
				this.maxCarLenght = 7;
				this.dynamicWidth = 25;
				if (color == 'linear-gradient(to bottom, #216fef, #0c4fc5)') {
					this.plateType = "blue";
					this.borderBgColor = "#fff";
					this.textColor = '#fff';
				} else if (color == 'linear-gradient(to bottom, #f8c401, #dba700)') {
					this.plateType = "yellow";
					this.borderBgColor = "#000";
					this.textColor = '#000';
				} else if (color == 'linear-gradient(to bottom, #f5f5f5, #e0e0e0)') {
					this.plateType = "white";
					this.borderBgColor = "#000";
					this.textColor = '#000';
				} else if (color == 'linear-gradient(to bottom, #525252, #1e1e1e)') {
					this.plateType = "black";
					this.borderBgColor = "#fff";
					this.textColor = '#fff';
				}
			}

			// 调整车牌字符数组长度以匹配新的车牌类型
			this.adjustPlateCharsLength();
			this.updateCurrentPlateChars();
		},

		// 清除所有车牌输入
		clearAllPlateInput() {
			this.formData.plateNumber = '';
			if (this.$refs.keyboardInput) {
				this.$refs.keyboardInput.toClear();
			}
			this.clearCurrentPlateChars();
		},

		// 调整车牌字符数组长度
		adjustPlateCharsLength() {
			const newLength = this.maxCarLenght;
			this.currentPlateChars = new Array(newLength).fill('');
		},

		// 更新当前车牌字符数组
		updateCurrentPlateChars() {
			if (this.$refs.keyboardInput && this.$refs.keyboardInput.values) {
				const values = this.$refs.keyboardInput.values;
				this.currentPlateChars = [...values];

				// 确保数组长度正确
				while (this.currentPlateChars.length < this.maxCarLenght) {
					this.currentPlateChars.push('');
				}

				// 更新表单数据
				this.formData.plateNumber = values.join('');
			}
		},

		// 清除当前车牌字符数组
		clearCurrentPlateChars() {
			this.currentPlateChars = new Array(this.maxCarLenght).fill('');
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f5f6fa;
	width: 100%;
}



/* 页面内容 */
.page-content {
	padding-top: 12rpx;
	padding-bottom: 24rpx;
	padding-left: 16rpx;
	padding-right: 16rpx;
}

/* 提交按钮区域 */
.submit-section {
	padding: 20rpx 16rpx;
	background: #ffffff;
	border-top: 1rpx solid #f0f0f0;
}

.submit-btn {
	width: 100%;
	height: 72rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #0081ff, #1890ff);
	border-radius: 8rpx;
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
	transition: all 0.3s ease;
	box-shadow: 0 2rpx 8rpx rgba(0, 129, 255, 0.3);
}

.submit-btn.disabled {
	opacity: 0.5;
	background: #cccccc;
}

/* 卡片样式 */
.section-card {
	background: #ffffff;
	border-radius: 10rpx;
	margin-bottom: 12rpx;
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

.section-header {
	display: flex;
	align-items: center;
	padding: 20rpx 20rpx 12rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.header-icon {
	width: 36rpx;
	height: 36rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 10rpx;
}

.header-icon .icon-emoji {
	font-size: 28rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

/* 车牌信息样式 */
.input-group {
	padding: 20rpx;
}

.input-wrapper {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 0 16rpx;
	margin-bottom: 12rpx;
}

.plate-input {
	flex: 1;
	height: 68rpx;
	font-size: 28rpx;
	color: #333333;
}

.scan-btn {
	display: flex;
	align-items: center;
	padding: 10rpx 16rpx;
	background: #0081ff;
	border-radius: 6rpx;
	margin-left: 10rpx;
}

.scan-btn .icon-emoji {
	font-size: 24rpx;
	margin-right: 8rpx;
}

.btn-text {
	font-size: 24rpx;
	color: #ffffff;
}

.owner-info {
	background: #e3f2fd;
	border-radius: 8rpx;
	padding: 12rpx;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.info-item:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 28rpx;
	color: #666666;
	margin-right: 16rpx;
}

.info-value {
	font-size: 28rpx;
	color: #1976d2;
	font-weight: 500;
}

/* 信用分样式 */
.credit-excellent {
	color: #52c41a !important;
	font-weight: 600;
}

.credit-warning {
	color: #faad14 !important;
	font-weight: 600;
}

.credit-danger {
	color: #ff4d4f !important;
	font-weight: 600;
}

/* 违规类型样式 */
.violation-types {
	padding: 20rpx;
}

/* 已选择类型显示 */
.selected-type {
	margin-bottom: 16rpx;
}

.selected-display {
	display: flex;
	align-items: center;
	padding: 12rpx 16rpx;
	background: #e3f2fd;
	border-radius: 8rpx;
	border: 1rpx solid #0081ff;
}

.selected-icon {
	font-size: 24rpx;
	margin-right: 12rpx;
}

.selected-name {
	flex: 1;
	font-size: 26rpx;
	color: #0081ff;
	font-weight: 600;
}

.selected-check {
	font-size: 20rpx;
	color: #0081ff;
	font-weight: bold;
}

/* 搜索区域 */
.search-section {
	margin-bottom: 16rpx;
}

.search-container {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.search-box {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 0 16rpx;
	border: 1rpx solid #e0e0e0;
	flex: 1;
}

.search-icon {
	font-size: 24rpx;
	color: #999999;
	margin-right: 12rpx;
}

.search-input {
	flex: 1;
	height: 64rpx;
	font-size: 26rpx;
	color: #333333;
}

.search-clear {
	padding: 8rpx;
	color: #999999;
	font-size: 28rpx;
	font-weight: bold;
}

.search-cancel {
	padding: 12rpx 16rpx;
}

.cancel-text {
	font-size: 26rpx;
	color: #666666;
}

/* 搜索结果 */
.search-results {
	margin-bottom: 16rpx;
}

/* 无搜索结果提示 */
.no-results {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx 20rpx;
	text-align: center;
}

.no-results-icon {
	font-size: 48rpx;
	color: #cccccc;
	margin-bottom: 16rpx;
}

.no-results-text {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.no-results-tip {
	font-size: 24rpx;
	color: #999999;
	line-height: 1.4;
}

/* 区域标签 */
.section-label {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 12rpx;
	font-weight: 500;
}

/* 标签容器 */
.type-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

/* 类型标签 */
.type-tag {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	background: #f0f9ff;
	border: 1rpx solid #0081ff;
	border-radius: 20rpx;
	font-size: 24rpx;
	color: #0081ff;
	transition: all 0.3s ease;
	min-height: 48rpx;
}

.type-tag.selected {
	background: #0081ff;
	color: #ffffff;
}

.type-tag.more {
	background: #f8f9fa;
	border-color: #cccccc;
	color: #666666;
}

.tag-icon {
	font-size: 20rpx;
	margin-right: 6rpx;
}

.tag-text {
	font-size: 22rpx;
	white-space: nowrap;
}

/* 分割线 */
.divider {
	height: 1rpx;
	background: #f0f0f0;
	margin: 16rpx 0;
}

/* 搜索入口 */
.search-entry {
	margin-top: 8rpx;
}

.search-trigger {
	display: flex;
	align-items: center;
	padding: 12rpx 16rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	border: 1rpx dashed #cccccc;
}

.search-trigger .search-icon {
	font-size: 24rpx;
	color: #999999;
	margin-right: 12rpx;
}

.search-text {
	font-size: 24rpx;
	color: #999999;
}

.custom-type-input {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 0 24rpx;
}

.custom-input {
	width: 100%;
	height: 88rpx;
	font-size: 28rpx;
	color: #333333;
}

/* 位置输入样式 */
.location-input-group {
	padding: 20rpx;
}

.location-wrapper {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 0 16rpx;
}

.location-input {
	flex: 1;
	height: 68rpx;
	font-size: 28rpx;
	color: #333333;
}

.location-btn {
	display: flex;
	align-items: center;
	padding: 10rpx 16rpx;
	background: #19be6b;
	border-radius: 6rpx;
	margin-left: 10rpx;
}

.location-btn .icon-emoji {
	font-size: 24rpx;
	margin-right: 8rpx;
}

/* 现场取证样式 */
.evidence-section {
	padding: 20rpx;
}

.photo-upload {
	margin-bottom: 20rpx;
}

.upload-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.upload-title {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
}

.photo-count {
	font-size: 24rpx;
	color: #999999;
}

.photo-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 10rpx;
}

.photo-item {
	position: relative;
	width: 100%;
	height: 120rpx;
	border-radius: 6rpx;
	overflow: hidden;
}

.photo-image {
	width: 100%;
	height: 100%;
}

.photo-delete {
	position: absolute;
	top: 8rpx;
	right: 8rpx;
	width: 32rpx;
	height: 32rpx;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.photo-delete .icon-emoji {
	font-size: 20rpx;
	color: #ffffff;
}

.photo-add {
	width: 100%;
	height: 120rpx;
	border: 2rpx dashed #cccccc;
	border-radius: 6rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #fafafa;
}

.add-icon {
	font-size: 40rpx;
	color: #cccccc;
	margin-bottom: 8rpx;
}

.add-text {
	font-size: 24rpx;
	color: #999999;
}



.voice-player {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	padding: 20rpx 24rpx;
	background: #e8f5e8;
	border-radius: 12rpx;
}

.voice-info {
	display: flex;
	align-items: center;
}

.voice-info .icon-emoji {
	font-size: 28rpx;
	margin-right: 12rpx;
	color: #19be6b;
}

.voice-duration {
	font-size: 28rpx;
	color: #19be6b;
	font-weight: 500;
}

.voice-actions {
	display: flex;
	gap: 16rpx;
}

.voice-action-btn {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #19be6b;
	border-radius: 24rpx;
}

.voice-action-btn.delete {
	background: #ff4d4f;
}

.voice-action-btn .icon-emoji {
	font-size: 24rpx;
}

/* 描述输入样式 */
.description-input {
	padding: 20rpx;
}

.description-textarea {
	width: 100%;
	min-height: 120rpx;
	padding: 16rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	font-size: 26rpx;
	color: #333333;
	line-height: 1.4;
}

/* 描述模板样式 */
.description-templates {
	margin-top: 20rpx;
}

.template-label {
	font-size: 24rpx;
	color: #909399;
	margin-bottom: 12rpx;
	display: block;
}

.template-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.template-tag {
	background: #f0f9ff;
	border: 2rpx solid #e1f5fe;
	border-radius: 20rpx;
	padding: 8rpx 16rpx;
	transition: all 0.2s ease;
}

.template-tag:active {
	background: #e3f2fd;
	border-color: #1976d2;
	transform: scale(0.95);
}

.template-text {
	font-size: 24rpx;
	color: #1976d2;
}

/* 车牌扫描弹窗样式 */
.scan-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
}

.scan-content {
	width: 90%;
	max-width: 700rpx;
	max-height: 90vh;
	background: #ffffff;
	border-radius: 16rpx;
	overflow: hidden;
}

.scan-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	background: #f8f9fa;
	border-bottom: 1rpx solid #f0f0f0;
}

.scan-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.scan-close {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f0f0f0;
	border-radius: 24rpx;
}

.scan-close .icon-emoji {
	font-size: 28rpx;
	color: #666666;
}

.scan-area {
	padding: 40rpx;
	text-align: center;
}

.scan-frame {
	width: 400rpx;
	height: 200rpx;
	border: 4rpx solid #0081ff;
	border-radius: 12rpx;
	margin: 0 auto 20rpx;
	position: relative;
	overflow: hidden;
}

.scan-line {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 4rpx;
	background: linear-gradient(90deg, transparent, #0081ff, transparent);
	animation: scan 2s linear infinite;
}

@keyframes scan {
	0% {
		transform: translateY(0);
	}
	100% {
		transform: translateY(196rpx);
	}
}

.scan-tip {
	font-size: 28rpx;
	color: #666666;
}

.scan-result {
	padding: 24rpx 40rpx;
	background: #f0f9ff;
	border-top: 1rpx solid #e6f7ff;
	text-align: center;
}

.result-label {
	font-size: 24rpx;
	color: #666666;
	margin-right: 12rpx;
}

.result-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #0081ff;
}

.scan-actions {
	display: flex;
	padding: 32rpx;
	gap: 16rpx;
}

.scan-action-btn {
	flex: 1;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: 500;
	background: #f8f9fa;
	color: #666666;
	transition: all 0.3s ease;
}

.scan-action-btn.primary {
	background: #0081ff;
	color: #ffffff;
}

.scan-action-btn.scanning {
	background: #ff9500;
	color: #ffffff;
}

/* 摄像头界面样式 */
.camera-container {
	position: relative;
	width: 100%;
	height: 500rpx;
	background: #000;
	border-radius: 12rpx;
	overflow: hidden;
}

.camera-preview {
	width: 100%;
	height: 100%;
	position: relative;
}

.plate-frame {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 300rpx;
	height: 120rpx;
	border: 4rpx solid #00ff00;
	border-radius: 8rpx;
}

.frame-corner {
	position: absolute;
	width: 40rpx;
	height: 40rpx;
	border: 6rpx solid #00ff00;
}

.frame-corner.tl {
	top: -6rpx;
	left: -6rpx;
	border-right: none;
	border-bottom: none;
}

.frame-corner.tr {
	top: -6rpx;
	right: -6rpx;
	border-left: none;
	border-bottom: none;
}

.frame-corner.bl {
	bottom: -6rpx;
	left: -6rpx;
	border-right: none;
	border-top: none;
}

.frame-corner.br {
	bottom: -6rpx;
	right: -6rpx;
	border-left: none;
	border-top: none;
}

.frame-text {
	position: absolute;
	bottom: -60rpx;
	left: 50%;
	transform: translateX(-50%);
	color: #fff;
	font-size: 28rpx;
	white-space: nowrap;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.8);
}

/* 自动识别状态指示器 */
.auto-status {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	display: flex;
	align-items: center;
	gap: 10rpx;
	background: rgba(0, 0, 0, 0.6);
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	backdrop-filter: blur(10rpx);
}

.status-dot {
	width: 20rpx;
	height: 20rpx;
	background: #00ff00;
	border-radius: 50%;
	animation: pulse 1.5s infinite;
}

@keyframes pulse {
	0% { opacity: 1; transform: scale(1); }
	50% { opacity: 0.7; transform: scale(1.2); }
	100% { opacity: 1; transform: scale(1); }
}

.status-text {
	color: #fff;
	font-size: 24rpx;
	font-weight: bold;
}

.camera-controls {
	display: flex;
	gap: 20rpx;
	padding: 20rpx;
	justify-content: center;
}

.capture-btn, .close-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 120rpx;
	height: 80rpx;
	border-radius: 12rpx;
	border: none;
	font-size: 24rpx;
	font-weight: bold;
	color: #fff;
	transition: all 0.3s ease;
}

.capture-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.capture-btn:disabled {
	background: #999;
	box-shadow: none;
	opacity: 0.5;
}

.close-btn {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
}

.camera-icon, .close-icon {
	font-size: 28rpx;
	margin-bottom: 8rpx;
}

.loading-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.7);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1002;
}

.loading-content {
	background: rgba(255, 255, 255, 0.9);
	padding: 40rpx 60rpx;
	border-radius: 20rpx;
	backdrop-filter: blur(10rpx);
}

.loading-text {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

/* 功能按钮样式 */
.function-buttons {
	padding: 20rpx;
}

/* ================ 车牌输入区域样式 ================ */

/* 车牌输入区域 */
.plate-input-section {
	margin-bottom: 20rpx;
}

/* 输入和操作按钮容器 */
.input-actions-container {
	display: flex;
	gap: 16rpx;
	align-items: flex-start;
}

/* 车牌搜索容器 */
.plate-search-container {
	position: relative;
	flex: 1;
}

/* 搜索输入框包装器 */
.search-input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	background: #ffffff;
	border: 2rpx solid #e4e7ed;
	border-radius: 12rpx;
	padding: 0 16rpx;
	transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
	border-color: #2979ff;
	box-shadow: 0 0 0 4rpx rgba(41, 121, 255, 0.1);
}

/* 搜索输入框 */
.plate-search-input {
	flex: 1;
	height: 88rpx;
	font-size: 32rpx;
	color: #303133;
	background: transparent;
	border: none;
	outline: none;
}

.plate-search-input::placeholder {
	color: #c0c4cc;
	font-size: 30rpx;
}

/* 搜索图标 */
.search-icon {
	position: absolute;
	right: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 清空按钮 */
.clear-btn {
	position: absolute;
	right: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	background: rgba(200, 201, 204, 0.1);
	transition: all 0.2s ease;
}

.clear-btn:active {
	background: rgba(200, 201, 204, 0.2);
	transform: scale(0.95);
}

/* 车牌识别按钮容器 */
.recognition-btn-container {
	flex-shrink: 0;
}

/* 车牌识别按钮 */
.plate-recognition-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 120rpx;
	height: 88rpx;
	background: #ffffff;
	border: 2rpx solid #2979ff;
	border-radius: 12rpx;
	transition: all 0.2s ease;
}

.plate-recognition-btn:active {
	background: #f0f7ff;
	transform: scale(0.98);
}

.plate-recognition-btn .btn-label {
	font-size: 24rpx;
	color: #2979ff;
	margin-top: 4rpx;
	font-weight: 500;
}

/* 搜索建议列表 */
.plate-suggestions {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background: #ffffff;
	border: 2rpx solid #e4e7ed;
	border-top: none;
	border-radius: 0 0 12rpx 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
	z-index: 9999;
	max-height: 400rpx;
	overflow-y: auto;
}

/* 建议项 */
.suggestion-item {
	display: flex;
	align-items: center;
	padding: 20rpx 16rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s ease;
}

.suggestion-item:last-child {
	border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item:active {
	background: #f5f7fa;
}

.suggestion-icon {
	margin-right: 12rpx;
	font-size: 28rpx;
}

.suggestion-text {
	flex: 1;
	font-size: 32rpx;
	color: #303133;
	font-weight: 500;
}

.suggestion-owner {
	font-size: 28rpx;
	color: #909399;
	margin-left: 12rpx;
}

/* 位置建议列表 */
.location-suggestions {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background: #ffffff;
	border: 2rpx solid #e4e7ed;
	border-top: none;
	border-radius: 0 0 12rpx 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
	z-index: 9999;
	max-height: 300rpx;
	overflow-y: auto;
}

/* 位置建议项 */
.location-suggestion-item {
	display: flex;
	align-items: center;
	padding: 20rpx 16rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s ease;
}

.location-suggestion-item:last-child {
	border-bottom: none;
}

.location-suggestion-item:hover,
.location-suggestion-item:active {
	background: #f5f7fa;
}

/* ================ 车牌识别弹窗样式 ================ */

.plate-recognition-modal {
	padding: 20rpx;
	text-align: center;
}

/* 摄像头容器 */
.camera-container {
	position: relative;
	width: 100%;
	height: 600rpx;
	border-radius: 12rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
}

.camera-preview {
	width: 100%;
	height: 100%;
	border-radius: 12rpx;
}

/* 车牌框选区域 */
.plate-frame {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 300rpx;
	height: 120rpx;
	border: 4rpx solid transparent;
}

.frame-corner {
	position: absolute;
	width: 40rpx;
	height: 40rpx;
	border: 4rpx solid #2979ff;
}

.frame-corner.tl {
	top: -4rpx;
	left: -4rpx;
	border-right: none;
	border-bottom: none;
}

.frame-corner.tr {
	top: -4rpx;
	right: -4rpx;
	border-left: none;
	border-bottom: none;
}

.frame-corner.bl {
	bottom: -4rpx;
	left: -4rpx;
	border-right: none;
	border-top: none;
}

.frame-corner.br {
	bottom: -4rpx;
	right: -4rpx;
	border-left: none;
	border-top: none;
}

.frame-text {
	position: absolute;
	bottom: -60rpx;
	left: 50%;
	transform: translateX(-50%);
	color: #2979ff;
	font-size: 24rpx;
	background: rgba(255, 255, 255, 0.9);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

/* 自动识别状态指示器 */
.auto-status {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	display: flex;
	align-items: center;
	background: rgba(0, 0, 0, 0.6);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.status-dot {
	width: 12rpx;
	height: 12rpx;
	background: #19be6b;
	border-radius: 50%;
	margin-right: 8rpx;
	animation: pulse 1.5s infinite;
}

@keyframes pulse {
	0% { opacity: 1; }
	50% { opacity: 0.5; }
	100% { opacity: 1; }
}

.auto-status .status-text {
	color: #ffffff;
	font-size: 24rpx;
}

/* 摄像头控制按钮 */
.camera-controls {
	display: flex;
	gap: 16rpx;
	justify-content: center;
	margin-top: 20rpx;
}

.control-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 120rpx;
	height: 100rpx;
	border-radius: 12rpx;
	transition: all 0.2s ease;
}

.control-btn text {
	font-size: 22rpx;
	margin-top: 8rpx;
}

.capture-btn {
	background: #2979ff;
	color: #ffffff;
}

.capture-btn:active {
	background: #1e5bb8;
	transform: scale(0.95);
}

.capture-btn.disabled {
	background: #c0c4cc;
	pointer-events: none;
}

.auto-btn {
	background: #f5f7fa;
	color: #606266;
	border: 2rpx solid #e4e7ed;
}

.auto-btn.active {
	background: #19be6b;
	color: #ffffff;
	border-color: #19be6b;
}

.auto-btn:active {
	transform: scale(0.95);
}

.close-btn {
	background: #f56c6c;
	color: #ffffff;
}

.close-btn:active {
	background: #e85a5a;
	transform: scale(0.95);
}

/* 加载遮罩 */
.loading-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 12rpx;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: #ffffff;
}

.loading-text {
	margin-top: 20rpx;
	font-size: 28rpx;
}

/* 识别选择界面 */
.recognition-options {
	padding: 20rpx 0;
}

.option-item {
	display: flex;
	align-items: center;
	padding: 24rpx 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 16rpx;
	transition: all 0.2s ease;
}

.option-item:last-child {
	margin-bottom: 0;
}

.option-item:active {
	background: #e9ecef;
	transform: scale(0.98);
}

.option-icon {
	margin-right: 20rpx;
}

.option-content {
	flex: 1;
	text-align: left;
}

.option-title {
	font-size: 32rpx;
	color: #303133;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.option-desc {
	font-size: 26rpx;
	color: #909399;
}

/* 识别结果 */
.recognition-result {
	margin-bottom: 40rpx;
	padding: 20rpx 0;
}

.result-header {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}

.result-title {
	font-size: 32rpx;
	color: #19be6b;
	font-weight: 500;
	margin-left: 12rpx;
}

.result-plate {
	background: #f5f7fa;
	border: 2rpx solid #e4e7ed;
	border-radius: 12rpx;
	padding: 24rpx;
	margin: 20rpx 0;
	text-align: center;
}

.plate-text {
	font-size: 40rpx;
	font-weight: bold;
	color: #303133;
	letter-spacing: 4rpx;
	margin-bottom: 12rpx;
}

.plate-color {
	font-size: 28rpx;
	color: #606266;
	margin-bottom: 8rpx;
}

.plate-confidence {
	font-size: 24rpx;
	color: #909399;
}

.result-actions {
	display: flex;
	gap: 20rpx;
	justify-content: center;
	margin-top: 30rpx;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	border: none;
	transition: all 0.2s ease;
}

.use-btn {
	background: #2979ff;
	color: #ffffff;
}

.use-btn:active {
	background: #1e5bb8;
}

.retry-btn {
	background: #f5f7fa;
	color: #606266;
	border: 2rpx solid #e4e7ed;
}

.retry-btn:active {
	background: #e4e7ed;
}

/* 识别操作按钮 */
.recognition-actions {
	margin-top: 40rpx;
}

.recognition-btn {
	width: 100%;
	height: 88rpx;
	background: #2979ff;
	color: #ffffff;
	border: none;
	border-radius: 12rpx;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.recognition-btn:active {
	background: #1e5bb8;
	transform: scale(0.98);
}

.recognition-btn.disabled {
	background: #c0c4cc;
	color: #ffffff;
	pointer-events: none;
}

.btn-text {
	margin-left: 12rpx;
}

/* ================ 车牌键盘样式 ================ */

/* 键盘输入容器 */
.keyboard-input-container {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 0 16rpx;
	margin-bottom: 12rpx;
}

.xm-keyboard-v2 {
	flex: 1;
	margin: 0;
}

.keyboard-input-wrapper {
	width: 100%;
	margin: 0;
}

/* 车牌类型选择器 */
.plate-type-selector {
	margin-top: 20rpx;
}

.color-car-button {
	display: flex;
	justify-content: space-between;
	gap: 10rpx;
	flex-wrap: wrap;
}

.blue-car, .yellow-car, .white-car, .black-car, .green-car {
	flex: 1;
	min-width: 100rpx;
	height: 60rpx;
	border-radius: 8rpx;
	border: 2rpx solid #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f8f9fa;
	transition: all 0.3s ease;
}

.blue-car-text, .yellow-car-text, .white-car-text, .black-car-text, .green-car-text {
	font-size: 24rpx;
	font-weight: bold;
	color: #666;
}

/* 车牌类型选择按钮样式 */
.blue-car.selected {
	background: linear-gradient(to bottom, #216fef, #0c4fc5) !important;
	border: 2rpx solid #216fef;
	color: #fff;
}

.blue-car.selected .blue-car-text {
	color: #fff;
}

.yellow-car.selected {
	background: linear-gradient(to bottom, #f8c401, #dba700) !important;
	border: 2rpx solid #f8c401;
}

.yellow-car.selected .yellow-car-text {
	color: #000;
}

.white-car.selected {
	background: linear-gradient(to bottom, #f5f5f5, #e0e0e0) !important;
	border: 2rpx solid #e0e0e0;
}

.white-car.selected .white-car-text {
	color: #000;
}

.black-car.selected {
	background: linear-gradient(to bottom, #525252, #1e1e1e) !important;
	border: 2rpx solid #000;
}

.black-car.selected .black-car-text {
	color: #fff;
}

.green-car.selected {
	background: linear-gradient(to bottom, #d0f1e4, #6ad390) !important;
	border: 2rpx solid #6ad390;
}

.green-car.selected .green-car-text {
	color: #000;
}

/* 确认弹窗样式 */
.confirm-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
}

.confirm-content {
	width: 80%;
	max-width: 500rpx;
	background: #ffffff;
	border-radius: 16rpx;
	overflow: hidden;
}

.confirm-header {
	padding: 32rpx 32rpx 24rpx;
	text-align: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.confirm-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.confirm-body {
	padding: 32rpx;
	text-align: center;
}

.confirm-text {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 24rpx;
}

.confirm-info {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 24rpx;
	text-align: left;
}

.info-text {
	display: block;
	font-size: 26rpx;
	color: #333333;
	margin-bottom: 8rpx;
}

.info-text:last-child {
	margin-bottom: 0;
}

.confirm-actions {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
}

.confirm-btn {
	flex: 1;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;
}

.confirm-btn.cancel {
	color: #666666;
	background: #f8f9fa;
	border-right: 1rpx solid #f0f0f0;
}

.confirm-btn.primary {
	color: #ffffff;
	background: #0081ff;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
	.type-grid {
		grid-template-columns: 1fr;
	}

	.photo-grid {
		grid-template-columns: repeat(2, 1fr);
	}
}
</style>
