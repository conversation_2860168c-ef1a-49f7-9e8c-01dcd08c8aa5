# 违规管理系统数据库设计与后端接口文档

## 1. 数据库设计

### 1.1 车主信息表 (owners)
```sql
CREATE TABLE owners (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '车主姓名',
    phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    address VARCHAR(200) COMMENT '住址',
    building VARCHAR(20) COMMENT '楼栋号',
    unit VARCHAR(20) COMMENT '单元号', 
    room VARCHAR(20) COMMENT '房间号',
    id_card VARCHAR(18) COMMENT '身份证号',
    credit_score INT DEFAULT 100 COMMENT '信用分',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_name (name)
);
```

### 1.2 车辆信息表 (vehicles)
```sql
CREATE TABLE vehicles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    plate_number VARCHAR(10) NOT NULL UNIQUE COMMENT '车牌号',
    owner_id BIGINT NOT NULL COMMENT '车主ID',
    is_new_energy BOOLEAN DEFAULT FALSE COMMENT '是否新能源车',
    vehicle_type ENUM('car', 'suv', 'truck', 'motorcycle') DEFAULT 'car' COMMENT '车辆类型',
    brand VARCHAR(50) COMMENT '品牌',
    model VARCHAR(50) COMMENT '型号',
    color VARCHAR(20) COMMENT '颜色',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES owners(id),
    INDEX idx_plate_number (plate_number),
    INDEX idx_owner_id (owner_id)
);
```

### 1.3 违规记录表 (violations)
```sql
CREATE TABLE violations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    plate_number VARCHAR(10) NOT NULL COMMENT '车牌号',
    owner_id BIGINT COMMENT '车主ID',
    violation_type VARCHAR(50) NOT NULL COMMENT '违规类型',
    custom_type VARCHAR(100) COMMENT '自定义违规类型',
    location VARCHAR(200) NOT NULL COMMENT '违规位置',
    description TEXT COMMENT '违规描述',
    appointment_time DATETIME COMMENT '预约时间',
    enter_time DATETIME COMMENT '进场时间',
    leave_time DATETIME COMMENT '离场时间',
    status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '处理状态',
    severity ENUM('mild', 'moderate', 'severe') DEFAULT 'moderate' COMMENT '严重程度',
    reporter_id BIGINT COMMENT '举报人ID',
    handler_id BIGINT COMMENT '处理人ID',
    photos JSON COMMENT '现场照片',
    voice_memo VARCHAR(500) COMMENT '语音备注文件路径',
    remark TEXT COMMENT '处理备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES owners(id),
    INDEX idx_plate_number (plate_number),
    INDEX idx_status (status),
    INDEX idx_violation_type (violation_type),
    INDEX idx_created_at (created_at)
);
```

### 1.4 违规类型配置表 (violation_types)
```sql
CREATE TABLE violation_types (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '违规类型名称',
    value VARCHAR(50) NOT NULL UNIQUE COMMENT '违规类型值',
    icon VARCHAR(10) COMMENT '图标',
    category ENUM('common', 'others') DEFAULT 'others' COMMENT '分类',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_usage_count (usage_count)
);
```

### 1.5 停车记录表 (parking_records)
```sql
CREATE TABLE parking_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    plate_number VARCHAR(10) NOT NULL COMMENT '车牌号',
    owner_id BIGINT COMMENT '车主ID',
    appointment_time DATETIME COMMENT '预约时间',
    appointment_reason VARCHAR(100) COMMENT '预约原因',
    enter_time DATETIME COMMENT '进场时间',
    leave_time DATETIME COMMENT '离场时间',
    parking_duration INT COMMENT '停车时长(分钟)',
    location VARCHAR(200) COMMENT '停车位置',
    status ENUM('not_entered', 'in_progress', 'completed') DEFAULT 'not_entered' COMMENT '停车状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES owners(id),
    INDEX idx_plate_number (plate_number),
    INDEX idx_status (status),
    INDEX idx_appointment_time (appointment_time)
);
```

### 1.6 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    role ENUM('owner', 'manager', 'admin') DEFAULT 'owner' COMMENT '角色',
    owner_id BIGINT COMMENT '关联车主ID',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    last_login_at TIMESTAMP COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES owners(id),
    INDEX idx_username (username),
    INDEX idx_role (role)
);
```

## 2. 后端接口设计

### 2.1 车主信息相关接口

#### 2.1.1 根据车牌号查询车主信息
```
GET /api/owners/by-plate/{plateNumber}
```
**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "name": "张三",
        "phone": "138****5678",
        "address": "8栋5单元555室",
        "creditScore": 85
    }
}
```

#### 2.1.2 车牌号搜索建议
```
GET /api/owners/plate-suggestions?keyword={keyword}
```
**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "plateNumber": "黑A12345",
            "ownerName": "张三"
        }
    ]
}
```

### 2.2 违规记录相关接口

#### 2.2.1 创建违规记录
```
POST /api/violations
```
**请求体:**
```json
{
    "plateNumber": "黑A12345",
    "violationType": "overtime",
    "customType": "",
    "location": "A区-15号车位",
    "description": "超时停车2小时",
    "photos": ["photo1.jpg", "photo2.jpg"],
    "voiceMemo": "voice.mp3"
}
```

#### 2.2.2 获取违规记录列表
```
GET /api/violations?page=1&size=20&plateNumber=&status=&violationType=
```

#### 2.2.3 更新违规记录状态
```
PUT /api/violations/{id}/status
```
**请求体:**
```json
{
    "status": "completed",
    "remark": "已处理完成"
}
```

### 2.3 违规类型相关接口

#### 2.3.1 获取违规类型列表
```
GET /api/violation-types
```
**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "common": [
            {
                "name": "超时停车",
                "value": "overtime",
                "icon": "🚗",
                "usage": 45
            }
        ],
        "others": [
            {
                "name": "未经授权停车",
                "value": "unauthorized",
                "icon": "🔒",
                "usage": 8
            }
        ]
    }
}
```

### 2.4 统计分析相关接口

#### 2.4.1 获取违规统计数据
```
GET /api/violations/statistics?startDate=2025-01-01&endDate=2025-01-31&plateNumber=
```

#### 2.4.2 获取高风险车辆列表
```
GET /api/violations/high-risk-vehicles?startDate=2025-01-01&endDate=2025-01-31
```

### 2.5 车牌识别相关接口

#### 2.5.1 车牌识别
```
POST /api/plate/recognize
```
**请求体:**
```json
{
    "image": "base64_encoded_image",
    "multiDetect": false
}
```
**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": true,
        "plateNumber": "黑A12345",
        "color": "蓝牌",
        "confidence": 95
    }
}
```

## 3. 数据初始化脚本

### 3.1 车主数据
```sql
INSERT INTO owners (name, phone, address, building, unit, room, credit_score) VALUES
('张三', '13812345678', '8栋5单元555室', '8', '5', '555', 85),
('李四', '13912345678', '5栋1单元105室', '5', '1', '105', 92),
('王五', '13712345678', '2栋7单元105室', '2', '7', '105', 78);
```

### 3.2 车辆数据
```sql
INSERT INTO vehicles (plate_number, owner_id, is_new_energy) VALUES
('黑A12345', 1, FALSE),
('黑B67890', 2, FALSE),
('黑AF57913', 3, TRUE);
```

### 3.3 违规类型数据
```sql
INSERT INTO violation_types (name, value, icon, category, usage_count, sort_order) VALUES
('超时停车', 'overtime', '🚗', 'common', 45, 1),
('未按位停车', 'wrong_position', '🅿️', 'common', 30, 2),
('占用他人车位', 'occupy_space', '🚫', 'common', 15, 3),
('遮挡车牌', 'block_plate', '🚫', 'common', 12, 4),
('未经授权停车', 'unauthorized', '🔒', 'others', 8, 5),
('堵塞通道', 'block_passage', '🚧', 'others', 6, 6),
('占用残疾人车位', 'disabled_space', '♿', 'others', 4, 7),
('逆向停车', 'reverse_parking', '🔄', 'others', 3, 8),
('跨线停车', 'cross_line', '📏', 'others', 3, 9),
('占用消防通道', 'fire_lane', '🚒', 'others', 2, 10),
('占用充电桩车位', 'charging_space', '🔌', 'others', 2, 11),
('车辆损坏', 'vehicle_damage', '🔧', 'others', 1, 12),
('其他', 'other', '➕', 'others', 1, 13);
```

## 4. 业务逻辑说明

### 4.1 信用分计算规则
- 初始信用分：100分
- 轻微违规：-2分（如未按位停车）
- 中等违规：-5分（如超时停车）
- 严重违规：-10分（如占用残疾人车位）
- 信用分等级：
  - 80-100分：优秀（绿色）
  - 60-79分：预警（橙色）
  - 0-59分：警告（红色）

### 4.2 违规处理流程
1. **待处理(pending)**: 新创建的违规记录
2. **处理中(processing)**: 管家正在处理
3. **已完成(completed)**: 处理完成
4. **已取消(cancelled)**: 取消处理

### 4.3 停车状态说明
- **未进场(not_entered)**: 有预约但未进场
- **在场中(in_progress)**: 已进场但未离场
- **已离场(completed)**: 已完成停车

## 5. API错误码定义

```json
{
    "200": "成功",
    "400": "请求参数错误",
    "401": "未授权",
    "403": "权限不足",
    "404": "资源不存在",
    "500": "服务器内部错误",
    "1001": "车牌号格式错误",
    "1002": "车主信息不存在",
    "1003": "违规类型不存在",
    "1004": "车牌识别失败",
    "1005": "图片格式不支持"
}
```

## 6. 数据库索引优化建议

### 6.1 复合索引
```sql
-- 违规记录查询优化
CREATE INDEX idx_violations_plate_status_time ON violations(plate_number, status, created_at);
CREATE INDEX idx_violations_type_time ON violations(violation_type, created_at);

-- 停车记录查询优化
CREATE INDEX idx_parking_plate_status_time ON parking_records(plate_number, status, appointment_time);
```

### 6.2 分区表建议
```sql
-- 按月分区违规记录表（适用于大数据量）
ALTER TABLE violations PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- ... 更多分区
);
```

## 7. 缓存策略

### 7.1 Redis缓存设计
```
# 车主信息缓存（30分钟）
owner:plate:{plateNumber} -> owner_info_json

# 违规类型缓存（1小时）
violation:types -> violation_types_json

# 统计数据缓存（10分钟）
stats:violations:{date_range} -> statistics_json

# 车牌搜索建议缓存（5分钟）
suggestions:plate:{keyword} -> suggestions_json
```

### 7.2 缓存更新策略
- 车主信息：写入时更新缓存
- 违规类型：定时刷新 + 手动清除
- 统计数据：定时计算 + 实时更新
- 搜索建议：LRU淘汰策略

## 8. 性能优化建议

### 8.1 数据库优化
1. 使用读写分离，统计查询走从库
2. 对历史数据进行归档，保持主表数据量适中
3. 使用连接池，避免频繁建立连接
4. 定期分析慢查询，优化SQL语句

### 8.2 接口优化
1. 分页查询，避免一次性返回大量数据
2. 使用异步处理耗时操作（如图片上传）
3. 实现接口限流，防止恶意请求
4. 使用CDN加速图片访问

## 9. 安全考虑

### 9.1 数据安全
1. 敏感信息加密存储（如身份证号）
2. 手机号脱敏显示
3. 图片文件安全检查
4. SQL注入防护

### 9.2 接口安全
1. JWT token认证
2. 接口权限控制
3. 请求参数校验
4. 防重放攻击

## 10. 监控告警

### 10.1 业务监控
- 违规记录创建成功率
- 车牌识别准确率
- 接口响应时间
- 数据库连接池状态

### 10.2 告警规则
- 接口错误率 > 5%
- 响应时间 > 2秒
- 数据库连接数 > 80%
- 磁盘使用率 > 85%
```
