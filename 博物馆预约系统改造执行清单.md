# 博物馆预约系统改造执行清单

## 📋 项目概况

**当前系统**：Vue 3 + Element Plus 停车管理系统  
**目标系统**：博物馆预约管理系统  
**改造方式**：删除无关功能 + 改造适用功能 + 新增博物馆功能  
**预计工期**：8周  

## 🗑️ 需要删除的文件（第一优先级）

### 页面组件删除清单
```bash
# 车辆相关（完全删除）
src/views/admin/VehicleReservation.vue
src/views/admin/VehicleReservationSuccess.vue
src/views/admin/YardInfo.vue
src/views/admin/MonthTicket.vue
src/views/admin/ReportCarIn.vue
src/views/admin/ReportCarOut.vue
src/views/admin/Parking.vue

# 设备管理相关（完全删除）
src/views/admin/DeviceInfo.vue
src/views/admin/DeviceMng.vue
src/views/admin/Maintenance.vue
src/views/admin/MaintenanceAudit.vue
src/views/admin/Allocation.vue
src/views/admin/AllocationAudit.vue
src/views/admin/Scrap.vue
src/views/admin/ScrapEdit.vue

# 其他无关功能（完全删除）
src/views/admin/Book.vue
src/views/admin/Butler.vue
src/views/admin/Patrol.vue
src/views/admin/Community.vue
src/views/admin/CommunitySet.vue
src/views/admin/OwnerInfo.vue
src/views/admin/Gate.vue
src/views/admin/NotifierInfo.vue
src/views/admin/IllegalRegiste.vue
src/views/admin/Venue.vue
src/views/admin/Purchase.vue
src/views/admin/PurchaseApproval.vue
src/views/admin/Supplier.vue
src/views/admin/AddSupplier.vue
```

### 路由配置清理
删除 `src/router/index.js` 中对应的路由配置（约200行代码）

## 🔄 需要改造的现有功能

### 1. 预约管理改造
**文件**：`src/views/admin/AppointAudit.vue`  
**改造为**：`src/views/admin/MuseumReservationAudit.vue`  
**主要变更**：
- 车牌号字段 → 身份证号字段
- 车辆信息 → 访客信息
- 停车时段 → 参观时段
- 新增同行人管理功能
- 新增年龄验证规则

### 2. 黑名单管理改造
**文件**：`src/views/admin/BlackList.vue`  
**改造为**：`src/views/admin/VisitorBlacklist.vue`  
**主要变更**：
- 车牌号 → 身份证号
- 违规类型 → 失信类型（逾期未取消）
- 新增自动拉黑规则（2次违规）
- 新增60天限制期管理

### 3. 查询统计改造
**文件**：`src/views/admin/Appointment.vue`  
**改造为**：`src/views/admin/ReservationQuery.vue`  
**主要变更**：
- 查询条件适配博物馆业务
- 新增访客类型筛选
- 新增参观状态管理

## ➕ 需要新开发的功能

### 前台用户界面
```
src/views/museum/
├── Home.vue                    # 博物馆首页（背景图、开放时间、导航）
├── PersonalReservation.vue     # 个人预约（身份证验证、同行人、时段选择）
├── GroupReservation.vue        # 团体预约（团体信息、Excel上传、审核）
├── PersonalCenter.vue          # 个人中心（预约查询、人员管理）
├── ReservationQuery.vue        # 预约查询（状态筛选、历史记录）
├── VisitorManagement.vue       # 人员管理（添加常用人员）
└── QRCodeDisplay.vue           # 二维码显示（60秒动态码）
```

### 后台管理界面
```
src/views/admin/museum/
├── ReservationManagement.vue   # 预约管理（审核、设置、统计）
├── VisitorStatistics.vue       # 访客统计（图表、数据分析）
├── SystemSettings.vue          # 系统设置（时间、规则、通知）
├── TimeSlotManagement.vue      # 时段管理（容量、开放时间）
└── DataExport.vue              # 数据导出（Excel、报表）
```

### 核心组件开发
```
src/components/museum/
├── ReservationCalendar.vue     # 预约日历（时段显示、剩余名额）
├── VisitorForm.vue             # 访客表单（身份证验证、同行人）
├── TimeSlotSelector.vue        # 时段选择器（容量限制、规则检查）
├── QRCodeGenerator.vue         # 二维码生成器（动态码、有效期）
├── IDCardValidator.vue         # 身份证验证器（格式、年龄、规则）
├── AgreementModal.vue          # 协议弹窗（隐私协议、须知）
└── StatisticsChart.vue         # 统计图表（ECharts、可视化）
```

## 🔧 技术实现要点

### 身份证验证规则
```javascript
// 核心验证逻辑
- 18位/15位格式验证
- 年龄自动计算（生日提取）
- 14周岁以下不可单独预约
- 60周岁以上无需预约
- 证件类型支持：身份证、护照、港澳通行证、外国人永久居留证
```

### 预约时段管理
```javascript
// 时段配置
周四、五：6个时段，每时段100人，总计600人
周六：6个时段，9点13点100人，其他150人，总计800人
时段：09:00-10:00, 10:00-11:00, 11:00-12:00, 12:00-13:00, 13:00-14:00, 14:00-15:30

// 放票规则
每周二早9:00放本周四五六的号
```

### 黑名单自动化
```javascript
// 自动拉黑规则
- 监控逾期未取消预约
- 累计2次违规自动拉黑
- 拉黑期限：60天
- 支持手动解除
```

### 二维码系统
```javascript
// 二维码规则
- 预约成功后生成
- 60秒有效期
- 一人一码
- 动态刷新
```

## 📅 开发计划（8周）

### 第一阶段（2周）：基础清理和架构
- [ ] 删除无关功能模块和文件
- [ ] 清理路由配置
- [ ] 搭建博物馆业务目录结构
- [ ] 改造用户认证系统
- [ ] 开发基础组件框架

### 第二阶段（3周）：核心功能开发
- [ ] 开发身份证验证组件
- [ ] 开发预约日历组件
- [ ] 实现个人预约功能
- [ ] 实现团体预约功能
- [ ] 开发预约管理后台

### 第三阶段（2周）：高级功能开发
- [ ] 实现二维码生成系统
- [ ] 开发黑名单管理功能
- [ ] 实现数据统计和可视化
- [ ] 开发系统设置功能
- [ ] 实现Excel导入导出

### 第四阶段（1周）：测试和优化
- [ ] 功能测试和Bug修复
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 部署配置和上线

## 🎯 保留的现有功能

### ✅ 完全保留（无需修改）
- 用户管理系统（User.vue、AddUser.vue）
- 角色权限管理（RoleManagement.vue、Permission.vue）
- 登录认证系统（Login.vue、auth.js）
- 基础组件（Header.vue、Sidebar.vue）
- 工具函数（request.js、validate.js）
- 客户管理（Customer.vue、AddCustomer.vue）
- 部门管理（Department.vue、AddDepartment.vue）

### 🔄 适配改造（修改业务逻辑）
- 预约审批系统
- 黑名单管理
- 查询统计功能

## 📊 数据库变更

### 新增数据表
- `museum_reservation` - 博物馆预约主表
- `companion_info` - 同行人信息表
- `visitor_blacklist` - 访客黑名单表
- `group_reservation` - 团体预约表
- `museum_config` - 系统配置表

### 删除数据表
- 所有车辆相关表
- 设备管理相关表
- 停车场相关表

## ⚠️ 风险提示

1. **数据迁移风险**：现有用户数据需要妥善保存
2. **业务逻辑复杂性**：预约规则较复杂，需充分测试
3. **并发处理**：高峰期预约可能出现并发问题
4. **身份证验证**：需要可靠的验证算法和API

## 🚀 预期效果

- **代码复用率**：40%（用户管理、权限系统等）
- **开发效率**：比从零开发节省50%时间
- **系统稳定性**：基于成熟框架，稳定性高
- **功能完整度**：100%满足博物馆预约需求

---

**建议**：按照此清单逐步执行，每完成一个阶段进行测试验收，确保改造质量。
