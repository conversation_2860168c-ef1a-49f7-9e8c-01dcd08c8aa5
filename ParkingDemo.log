2025-07-29 08:12:35.286  INFO 31208 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 31208 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-07-29 08:12:35.289  INFO 31208 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-07-29 08:12:35.316  INFO 31208 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-29 08:12:35.316  INFO 31208 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-29 08:12:36.355  INFO 31208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : <PERSON><PERSON> initialized with port(s): 8543 (http)
2025-07-29 08:12:36.360  INFO 31208 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-29 08:12:36.361  INFO 31208 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-29 08:12:36.411  INFO 31208 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-29 08:12:36.411  INFO 31208 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1094 ms
2025-07-29 08:12:36.811  WARN 31208 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-07-29 08:12:37.937  INFO 31208 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-29 08:12:37.963  INFO 31208 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-29 08:12:38.003  INFO 31208 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-29 08:12:38.164  INFO 31208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-07-29 08:12:38.164  INFO 31208 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-07-29 08:12:38.175  INFO 31208 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-07-29 08:12:38.204  INFO 31208 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-07-29 08:12:38.314  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-07-29 08:12:38.317  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-07-29 08:12:38.324  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-07-29 08:12:38.325  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-07-29 08:12:38.329  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-07-29 08:12:38.332  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-07-29 08:12:38.342  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-07-29 08:12:38.343  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-07-29 08:12:38.344  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-07-29 08:12:38.344  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-07-29 08:12:38.345  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-07-29 08:12:38.347  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-07-29 08:12:38.347  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-07-29 08:12:38.348  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-07-29 08:12:38.348  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-07-29 08:12:38.349  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-07-29 08:12:38.350  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-07-29 08:12:38.351  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-07-29 08:12:38.353  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-07-29 08:12:38.353  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-07-29 08:12:38.354  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-07-29 08:12:38.356  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-07-29 08:12:38.357  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-07-29 08:12:38.362  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-07-29 08:12:38.362  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-07-29 08:12:38.364  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-07-29 08:12:38.366  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-07-29 08:12:38.367  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-07-29 08:12:38.367  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-07-29 08:12:38.368  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-07-29 08:12:38.370  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-07-29 08:12:38.371  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-07-29 08:12:38.371  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-07-29 08:12:38.373  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-07-29 08:12:38.374  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-07-29 08:12:38.375  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-07-29 08:12:38.378  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-07-29 08:12:38.378  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-07-29 08:12:38.380  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-07-29 08:12:38.381  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-07-29 08:12:38.384  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-07-29 08:12:38.384  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-07-29 08:12:38.385  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-07-29 08:12:38.385  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-07-29 08:12:38.389  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-07-29 08:12:38.391  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-07-29 08:12:38.392  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-07-29 08:12:38.392  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-07-29 08:12:38.393  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-07-29 08:12:38.393  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-07-29 08:12:38.394  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-07-29 08:12:38.394  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-07-29 08:12:38.395  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-07-29 08:12:38.396  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-07-29 08:12:38.396  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-07-29 08:12:38.404  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-07-29 08:12:38.405  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-07-29 08:12:38.406  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-07-29 08:12:38.407  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-07-29 08:12:38.408  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-07-29 08:12:38.411  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-07-29 08:12:38.411  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-07-29 08:12:38.419  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-07-29 08:12:38.419  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-07-29 08:12:38.420  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-07-29 08:12:38.421  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-07-29 08:12:38.421  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-07-29 08:12:38.421  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-07-29 08:12:38.422  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-07-29 08:12:38.423  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-07-29 08:12:38.424  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-07-29 08:12:38.425  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-07-29 08:12:38.425  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-07-29 08:12:38.427  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-07-29 08:12:38.428  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-07-29 08:12:38.429  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-07-29 08:12:38.430  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-07-29 08:12:38.431  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-07-29 08:12:38.433  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-07-29 08:12:38.436  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-07-29 08:12:38.437  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-07-29 08:12:38.437  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-07-29 08:12:38.438  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-07-29 08:12:38.439  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-07-29 08:12:38.440  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-07-29 08:12:38.441  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-07-29 08:12:38.442  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-07-29 08:12:38.443  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-07-29 08:12:38.443  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-07-29 08:12:38.443  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-07-29 08:12:38.444  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-07-29 08:12:38.444  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-07-29 08:12:38.444  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-07-29 08:12:38.445  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-07-29 08:12:38.445  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-07-29 08:12:38.445  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-07-29 08:12:38.446  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-07-29 08:12:38.446  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-07-29 08:12:38.446  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-07-29 08:12:38.447  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-07-29 08:12:38.447  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-07-29 08:12:38.448  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-07-29 08:12:38.450  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-07-29 08:12:38.451  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-07-29 08:12:38.452  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-07-29 08:12:38.453  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-07-29 08:12:38.454  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-07-29 08:12:38.454  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-07-29 08:12:38.456  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-07-29 08:12:38.457  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-07-29 08:12:38.458  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-07-29 08:12:38.459  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-07-29 08:12:38.459  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-07-29 08:12:38.460  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-07-29 08:12:38.460  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-07-29 08:12:38.464  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-07-29 08:12:38.465  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-07-29 08:12:38.465  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-07-29 08:12:38.467  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-07-29 08:12:38.469  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-07-29 08:12:38.470  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-07-29 08:12:38.474  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-07-29 08:12:38.477  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-07-29 08:12:38.493  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-07-29 08:12:38.498  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-07-29 08:12:38.498  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-07-29 08:12:38.499  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-07-29 08:12:38.500  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-07-29 08:12:38.500  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-07-29 08:12:38.500  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-07-29 08:12:38.500  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-07-29 08:12:38.505  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-07-29 08:12:38.506  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-07-29 08:12:38.508  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-07-29 08:12:38.522  INFO 31208 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 3.464 seconds (JVM running for 4.097)
2025-07-29 08:12:39.469  INFO 31208 --- [RMI TCP Connection(4)-*************] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-07-29 08:12:39.790  INFO 31208 --- [RMI TCP Connection(5)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 08:12:39.791  INFO 31208 --- [RMI TCP Connection(5)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-29 08:12:39.791  INFO 31208 --- [RMI TCP Connection(5)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-07-29 08:37:14.584  WARN 31208 --- [http-nio-8543-exec-1] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 1475004
2025-07-29 08:44:32.149  WARN 31208 --- [http-nio-8543-exec-8] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 374112
2025-07-29 08:48:53.244  WARN 31208 --- [http-nio-8543-exec-1] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 260945
2025-07-29 08:55:50.305  WARN 31208 --- [http-nio-8543-exec-9] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 242652
2025-07-29 08:58:25.962  WARN 31208 --- [http-nio-8543-exec-9] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 73546
2025-07-29 09:01:53.646  WARN 31208 --- [http-nio-8543-exec-7] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 157747
2025-07-29 09:04:25.210  WARN 31208 --- [http-nio-8543-exec-10] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 151436
2025-07-29 09:14:00.418  WARN 31208 --- [http-nio-8543-exec-3] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 575002
2025-07-29 09:53:15.831  WARN 31208 --- [http-nio-8543-exec-9] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 188135
2025-07-29 10:12:48.476  WARN 31208 --- [http-nio-8543-exec-4] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 1170082
2025-07-29 10:25:15.232  INFO 31208 --- [Thread-11] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-07-29 10:25:15.235  INFO 31208 --- [Thread-11] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-07-29 10:25:15.443  INFO 31208 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 31208 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-07-29 10:25:15.444  INFO 31208 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-07-29 10:25:15.843  INFO 31208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8543 (http)
2025-07-29 10:25:15.843  INFO 31208 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-29 10:25:15.843  INFO 31208 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-29 10:25:15.856  INFO 31208 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-29 10:25:15.856  INFO 31208 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 411 ms
2025-07-29 10:25:15.954  WARN 31208 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-07-29 10:25:16.657  INFO 31208 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-29 10:25:16.667  INFO 31208 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-29 10:25:16.678  INFO 31208 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-29 10:25:16.728  INFO 31208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-07-29 10:25:16.728  INFO 31208 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-07-29 10:25:16.729  INFO 31208 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-07-29 10:25:16.739  INFO 31208 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-07-29 10:25:16.767  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-07-29 10:25:16.768  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-07-29 10:25:16.773  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-07-29 10:25:16.773  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-07-29 10:25:16.776  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-07-29 10:25:16.778  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-07-29 10:25:16.785  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-07-29 10:25:16.786  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-07-29 10:25:16.787  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-07-29 10:25:16.787  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-07-29 10:25:16.788  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-07-29 10:25:16.788  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-07-29 10:25:16.789  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-07-29 10:25:16.789  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-07-29 10:25:16.789  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-07-29 10:25:16.790  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-07-29 10:25:16.791  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-07-29 10:25:16.792  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-07-29 10:25:16.792  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-07-29 10:25:16.793  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-07-29 10:25:16.794  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-07-29 10:25:16.795  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-07-29 10:25:16.795  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-07-29 10:25:16.798  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-07-29 10:25:16.798  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-07-29 10:25:16.799  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-07-29 10:25:16.802  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-07-29 10:25:16.804  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-07-29 10:25:16.804  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-07-29 10:25:16.805  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-07-29 10:25:16.806  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-07-29 10:25:16.806  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-07-29 10:25:16.807  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-07-29 10:25:16.808  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-07-29 10:25:16.809  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-07-29 10:25:16.809  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-07-29 10:25:16.812  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-07-29 10:25:16.812  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-07-29 10:25:16.813  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-07-29 10:25:16.814  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-07-29 10:25:16.816  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-07-29 10:25:16.816  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-07-29 10:25:16.816  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-07-29 10:25:16.816  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-07-29 10:25:16.819  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-07-29 10:25:16.820  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-07-29 10:25:16.820  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-07-29 10:25:16.820  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-07-29 10:25:16.821  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-07-29 10:25:16.821  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-07-29 10:25:16.822  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-07-29 10:25:16.822  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-07-29 10:25:16.822  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-07-29 10:25:16.823  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-07-29 10:25:16.823  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-07-29 10:25:16.828  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-07-29 10:25:16.828  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-07-29 10:25:16.828  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-07-29 10:25:16.829  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-07-29 10:25:16.829  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-07-29 10:25:16.831  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-07-29 10:25:16.831  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-07-29 10:25:16.835  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-07-29 10:25:16.835  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-07-29 10:25:16.836  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-07-29 10:25:16.836  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-07-29 10:25:16.836  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-07-29 10:25:16.836  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-07-29 10:25:16.837  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-07-29 10:25:16.838  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-07-29 10:25:16.838  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-07-29 10:25:16.839  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-07-29 10:25:16.839  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-07-29 10:25:16.840  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-07-29 10:25:16.841  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-07-29 10:25:16.841  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-07-29 10:25:16.842  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-07-29 10:25:16.843  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-07-29 10:25:16.845  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-07-29 10:25:16.847  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-07-29 10:25:16.847  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-07-29 10:25:16.847  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-07-29 10:25:16.848  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-07-29 10:25:16.848  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-07-29 10:25:16.850  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-07-29 10:25:16.850  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-07-29 10:25:16.851  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-07-29 10:25:16.852  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-07-29 10:25:16.852  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-07-29 10:25:16.852  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-07-29 10:25:16.853  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-07-29 10:25:16.853  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-07-29 10:25:16.853  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-07-29 10:25:16.854  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-07-29 10:25:16.854  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-07-29 10:25:16.854  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-07-29 10:25:16.854  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-07-29 10:25:16.854  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-07-29 10:25:16.855  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-07-29 10:25:16.855  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-07-29 10:25:16.855  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-07-29 10:25:16.855  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-07-29 10:25:16.857  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-07-29 10:25:16.858  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-07-29 10:25:16.859  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-07-29 10:25:16.868  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-07-29 10:25:16.869  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-07-29 10:25:16.869  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-07-29 10:25:16.870  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-07-29 10:25:16.871  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-07-29 10:25:16.871  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-07-29 10:25:16.872  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-07-29 10:25:16.873  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-07-29 10:25:16.873  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-07-29 10:25:16.873  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-07-29 10:25:16.875  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-07-29 10:25:16.876  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-07-29 10:25:16.876  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-07-29 10:25:16.878  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-07-29 10:25:16.879  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-07-29 10:25:16.879  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-07-29 10:25:16.882  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-07-29 10:25:16.885  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-07-29 10:25:16.898  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-07-29 10:25:16.904  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-07-29 10:25:16.904  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-07-29 10:25:16.905  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-07-29 10:25:16.905  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-07-29 10:25:16.906  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-07-29 10:25:16.906  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-07-29 10:25:16.906  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-07-29 10:25:16.910  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-07-29 10:25:16.911  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-07-29 10:25:16.912  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-07-29 10:25:16.916  INFO 31208 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 1.493 seconds (JVM running for 7962.491)
2025-07-29 10:25:16.918  INFO 31208 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-07-29 10:25:18.415  INFO 31208 --- [Thread-27] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
2025-07-29 10:25:18.623  INFO 31208 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 31208 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-07-29 10:25:18.623  INFO 31208 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-07-29 10:25:18.866  INFO 31208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8543 (http)
2025-07-29 10:25:18.867  INFO 31208 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-29 10:25:18.867  INFO 31208 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-29 10:25:18.878  INFO 31208 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-29 10:25:18.878  INFO 31208 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 254 ms
2025-07-29 10:25:18.963  WARN 31208 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-07-29 10:25:19.547  INFO 31208 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-29 10:25:19.555  INFO 31208 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-29 10:25:19.565  INFO 31208 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-29 10:25:19.614  INFO 31208 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-07-29 10:25:19.615  INFO 31208 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-07-29 10:25:19.615  INFO 31208 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-07-29 10:25:19.622  INFO 31208 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-07-29 10:25:19.649  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-07-29 10:25:19.651  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-07-29 10:25:19.654  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-07-29 10:25:19.654  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-07-29 10:25:19.657  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-07-29 10:25:19.658  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-07-29 10:25:19.664  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-07-29 10:25:19.664  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-07-29 10:25:19.665  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-07-29 10:25:19.665  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-07-29 10:25:19.665  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-07-29 10:25:19.666  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-07-29 10:25:19.666  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-07-29 10:25:19.667  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-07-29 10:25:19.667  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-07-29 10:25:19.668  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-07-29 10:25:19.668  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-07-29 10:25:19.669  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-07-29 10:25:19.669  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-07-29 10:25:19.670  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-07-29 10:25:19.670  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-07-29 10:25:19.672  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-07-29 10:25:19.672  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-07-29 10:25:19.675  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-07-29 10:25:19.675  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-07-29 10:25:19.676  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-07-29 10:25:19.677  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-07-29 10:25:19.678  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-07-29 10:25:19.679  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-07-29 10:25:19.679  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-07-29 10:25:19.680  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-07-29 10:25:19.681  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-07-29 10:25:19.681  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-07-29 10:25:19.682  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-07-29 10:25:19.682  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-07-29 10:25:19.683  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-07-29 10:25:19.685  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-07-29 10:25:19.685  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-07-29 10:25:19.686  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-07-29 10:25:19.687  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-07-29 10:25:19.689  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-07-29 10:25:19.689  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-07-29 10:25:19.689  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-07-29 10:25:19.689  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-07-29 10:25:19.692  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-07-29 10:25:19.693  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-07-29 10:25:19.693  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-07-29 10:25:19.693  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-07-29 10:25:19.694  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-07-29 10:25:19.694  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-07-29 10:25:19.694  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-07-29 10:25:19.694  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-07-29 10:25:19.695  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-07-29 10:25:19.696  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-07-29 10:25:19.696  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-07-29 10:25:19.700  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-07-29 10:25:19.700  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-07-29 10:25:19.700  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-07-29 10:25:19.701  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-07-29 10:25:19.701  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-07-29 10:25:19.703  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-07-29 10:25:19.703  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-07-29 10:25:19.706  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-07-29 10:25:19.707  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-07-29 10:25:19.707  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-07-29 10:25:19.707  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-07-29 10:25:19.708  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-07-29 10:25:19.708  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-07-29 10:25:19.708  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-07-29 10:25:19.709  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-07-29 10:25:19.709  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-07-29 10:25:19.710  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-07-29 10:25:19.710  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-07-29 10:25:19.711  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-07-29 10:25:19.711  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-07-29 10:25:19.712  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-07-29 10:25:19.713  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-07-29 10:25:19.714  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-07-29 10:25:19.715  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-07-29 10:25:19.717  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-07-29 10:25:19.717  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-07-29 10:25:19.718  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-07-29 10:25:19.718  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-07-29 10:25:19.718  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-07-29 10:25:19.720  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-07-29 10:25:19.720  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-07-29 10:25:19.721  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-07-29 10:25:19.721  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-07-29 10:25:19.722  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-07-29 10:25:19.722  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-07-29 10:25:19.722  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-07-29 10:25:19.722  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-07-29 10:25:19.722  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-07-29 10:25:19.723  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-07-29 10:25:19.723  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-07-29 10:25:19.723  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-07-29 10:25:19.723  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-07-29 10:25:19.724  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-07-29 10:25:19.724  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-07-29 10:25:19.724  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-07-29 10:25:19.724  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-07-29 10:25:19.724  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-07-29 10:25:19.726  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-07-29 10:25:19.727  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-07-29 10:25:19.727  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-07-29 10:25:19.728  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-07-29 10:25:19.728  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-07-29 10:25:19.728  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-07-29 10:25:19.730  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-07-29 10:25:19.730  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-07-29 10:25:19.730  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-07-29 10:25:19.731  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-07-29 10:25:19.732  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-07-29 10:25:19.732  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-07-29 10:25:19.732  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-07-29 10:25:19.735  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-07-29 10:25:19.735  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-07-29 10:25:19.735  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-07-29 10:25:19.737  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-07-29 10:25:19.738  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-07-29 10:25:19.738  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-07-29 10:25:19.741  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-07-29 10:25:19.743  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-07-29 10:25:19.755  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-07-29 10:25:19.759  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-07-29 10:25:19.760  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-07-29 10:25:19.760  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-07-29 10:25:19.760  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-07-29 10:25:19.761  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-07-29 10:25:19.761  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-07-29 10:25:19.761  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-07-29 10:25:19.764  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-07-29 10:25:19.765  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-07-29 10:25:19.766  INFO 31208 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-07-29 10:25:19.770  INFO 31208 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 1.163 seconds (JVM running for 7965.345)
2025-07-29 10:25:19.771  INFO 31208 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-07-29 11:55:08.023  INFO 31208 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
