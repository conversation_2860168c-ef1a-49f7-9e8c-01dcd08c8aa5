# 博物馆预约系统改造开发计划 - 详细分析版

## 一、项目现状分析

### 1.1 当前项目概况
当前项目是一个基于Vue 3 + Element Plus的前端管理系统，主要用于**停车场管理**，包含以下核心模块：

**技术栈**：
- 前端：Vue 3.2.14 + Element Plus 1.1.0 + Vue Router 4 + Vuex 4
- 构建工具：Vue CLI 4.5.0
- 样式：Sass + Element Plus组件库
- 其他：Axios、ECharts、二维码生成等

**现有功能模块**：
1. **系统管理**：用户管理、角色管理、权限管理
2. **小区管理**：业主管理、客户管理、部门管理
3. **车辆管理**：外来车辆预约、车场信息、黑名单管理
4. **预约审批**：预约审核、用户审批流程
5. **查询统计**：预约查询、入场查询、违规查询

### 1.2 可复用的功能模块

**✅ 可以直接复用的模块**：
- 用户管理系统（User.vue、AddUser.vue）
- 角色权限管理（RoleManagement.vue、Permission.vue）
- 登录认证系统（Login.vue、auth.js）
- 基础组件（Header.vue、Sidebar.vue）
- 工具函数（request.js、validate.js）

**🔄 需要适配改造的模块**：
- 预约管理系统（需要从车辆预约改为博物馆预约）
- 黑名单管理（从车牌黑名单改为身份证黑名单）
- 查询统计功能（适配博物馆业务场景）

**❌ 需要删除的模块**：
- 车辆相关的所有功能（VehicleReservation.vue、YardInfo.vue等）
- 停车场相关功能（Parking.vue、MonthTicket.vue等）
- 设备管理相关（DeviceInfo.vue、DeviceMng.vue等）

## 二、博物馆预约系统需求分析

### 2.1 核心功能需求

**前台用户功能**：
1. **首页展示**：博物馆背景、开闭馆时间、地址、导航菜单
2. **个人预约**：实名认证、身份证验证、同行人管理、时段选择
3. **团体预约**：团体信息、成员管理、Excel上传、审核流程
4. **个人中心**：预约查询、人员管理、预约取消
5. **二维码系统**：动态二维码生成、60秒有效期

**后台管理功能**：
1. **预约管理**：个人/团体预约审核、预约设置
2. **黑名单管理**：失信人员管理、自动拉黑规则
3. **数据统计**：访客统计、可视化报表、数据导出
4. **系统设置**：开放时间、预约规则、通知管理

### 2.2 特殊业务规则

**预约规则**：
- 每周二9:00放本周四五六的号
- 14周岁以下不可单独预约
- 一人最多预约5人
- 60周岁以上无需预约
- 累计2次逾期未取消将被拉黑60天

**时段管理**：
- 6个时段：9:00-10:00, 10:00-11:00, 11:00-12:00, 12:00-13:00, 13:00-14:00, 14:00-15:30
- 周四五：每时段100人，总计600人
- 周六：9点13点100人/时段，其他150人/时段，总计800人

## 三、改造开发计划

### 3.1 需要删除的文件和功能

**完全删除的页面组件**：
```
src/views/admin/
├── VehicleReservation.vue          # 外来车辆预约
├── VehicleReservationSuccess.vue   # 车辆放行管理
├── YardInfo.vue                    # 车场信息管理
├── MonthTicket.vue                 # 月票管理
├── ReportCarIn.vue                 # 车辆入场记录
├── ReportCarOut.vue                # 车辆离场记录
├── Parking.vue                     # 停车管理
├── DeviceInfo.vue                  # 设备信息
├── DeviceMng.vue                   # 设备管理
├── Maintenance.vue                 # 报修申请
├── MaintenanceAudit.vue            # 报修审批
├── Allocation.vue                  # 调拨申请
├── AllocationAudit.vue             # 调拨审批
├── Scrap.vue                       # 报废申请
├── ScrapEdit.vue                   # 报废审核
├── Purchase.vue                    # 采购管理
├── PurchaseApproval.vue            # 采购审批
├── Supplier.vue                    # 供应商管理
├── AddSupplier.vue                 # 供应商编辑
├── Book.vue                        # 书籍管理
├── Butler.vue                      # 管家管理
├── Patrol.vue                      # 巡逻员管理
├── Community.vue                   # 小区管理
├── CommunitySet.vue                # 小区设置
├── OwnerInfo.vue                   # 业主管理
├── Gate.vue                        # 出入口系统
├── NotifierInfo.vue                # 商场信息
├── IllegalRegiste.vue              # 违规查询
└── Venue.vue                       # 入场查询
```

**需要清理的路由配置**：
- 删除router/index.js中所有车辆、停车、设备相关的路由
- 保留用户管理、权限管理、基础查询等通用路由

### 3.2 需要改造的现有功能

**1. 预约管理系统改造**：
- **原功能**：AppointAudit.vue（车辆预约审批）
- **改造为**：MuseumReservationAudit.vue（博物馆预约审批）
- **主要变更**：
  - 车牌号 → 身份证号
  - 车辆信息 → 访客信息
  - 停车时段 → 参观时段
  - 增加同行人管理
  - 增加年龄验证规则

**2. 黑名单管理改造**：
- **原功能**：BlackList.vue（车牌黑名单）
- **改造为**：VisitorBlacklist.vue（访客黑名单）
- **主要变更**：
  - 车牌号 → 身份证号
  - 违规类型 → 失信类型
  - 增加自动拉黑规则
  - 增加解除黑名单功能

**3. 查询统计改造**：
- **原功能**：Appointment.vue（预约查询）
- **改造为**：ReservationQuery.vue（预约查询）
- **主要变更**：
  - 查询条件适配博物馆业务
  - 增加访客类型筛选
  - 增加参观状态管理

### 3.3 需要新开发的功能模块

**前台用户界面**：
```
src/views/museum/
├── Home.vue                        # 博物馆首页
├── PersonalReservation.vue         # 个人预约
├── GroupReservation.vue            # 团体预约
├── PersonalCenter.vue              # 个人中心
├── ReservationQuery.vue            # 预约查询
├── VisitorManagement.vue           # 人员管理
└── QRCodeDisplay.vue               # 二维码显示
```

**后台管理界面**：
```
src/views/admin/museum/
├── ReservationManagement.vue       # 预约管理
├── VisitorStatistics.vue           # 访客统计
├── SystemSettings.vue              # 系统设置
├── NoticeManagement.vue            # 通知管理
├── TimeSlotManagement.vue          # 时段管理
└── DataExport.vue                  # 数据导出
```

**公共组件**：
```
src/components/museum/
├── ReservationCalendar.vue         # 预约日历
├── VisitorForm.vue                 # 访客信息表单
├── TimeSlotSelector.vue            # 时段选择器
├── QRCodeGenerator.vue             # 二维码生成器
├── IDCardValidator.vue             # 身份证验证器
├── AgreementModal.vue              # 协议弹窗
└── StatisticsChart.vue             # 统计图表
```

## 四、技术实现要点

### 4.1 身份证验证系统
- 身份证格式验证（18位/15位）
- 年龄自动计算和验证
- 14周岁以下限制规则
- 60周岁以上免预约规则

### 4.2 预约时段管理
- 动态时段配置
- 剩余名额实时计算
- 时段冲突检测
- 自动释放机制

### 4.3 二维码系统
- 动态二维码生成
- 60秒有效期控制
- 一人一码机制
- 二维码验证接口

### 4.4 黑名单自动化
- 逾期未取消自动检测
- 2次违规自动拉黑
- 60天限制期管理
- 黑名单状态同步

## 五、开发优先级和时间估算

### 第一阶段（2周）：基础架构搭建
1. 清理无关功能模块
2. 搭建博物馆业务架构
3. 改造用户认证系统
4. 开发基础组件

### 第二阶段（3周）：核心功能开发
1. 个人预约功能
2. 团体预约功能
3. 预约管理后台
4. 身份证验证系统

### 第三阶段（2周）：高级功能开发
1. 二维码系统
2. 黑名单管理
3. 数据统计分析
4. 系统设置功能

### 第四阶段（1周）：测试和优化
1. 功能测试
2. 性能优化
3. 用户体验优化
4. 部署上线

**总计开发时间：8周**

## 六、风险评估和建议

### 6.1 技术风险
- 身份证验证API的稳定性
- 二维码生成的性能问题
- 高并发预约的数据一致性

### 6.2 业务风险
- 预约规则的复杂性
- 用户体验的适配性
- 数据迁移的完整性

### 6.3 建议
1. 保留现有用户管理系统，减少开发风险
2. 分阶段开发，确保每个阶段都有可用版本
3. 充分测试身份证验证和预约规则
4. 建立完善的数据备份和恢复机制

## 七、详细技术实现方案

### 7.1 数据库设计变更

**新增数据表**：
```sql
-- 博物馆预约表
CREATE TABLE museum_reservation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_no VARCHAR(32) UNIQUE NOT NULL COMMENT '预约编号',
    visitor_name VARCHAR(50) NOT NULL COMMENT '预约人姓名',
    visitor_id_card VARCHAR(18) NOT NULL COMMENT '预约人身份证',
    visitor_phone VARCHAR(11) COMMENT '联系电话',
    visit_date DATE NOT NULL COMMENT '参观日期',
    time_slot VARCHAR(20) NOT NULL COMMENT '时段',
    visitor_count INT DEFAULT 1 COMMENT '参观人数',
    reservation_type ENUM('PERSONAL', 'GROUP') DEFAULT 'PERSONAL' COMMENT '预约类型',
    status ENUM('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED') DEFAULT 'PENDING' COMMENT '状态',
    qr_code VARCHAR(255) COMMENT '二维码',
    qr_expire_time DATETIME COMMENT '二维码过期时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 同行人信息表
CREATE TABLE companion_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约ID',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    id_card VARCHAR(18) NOT NULL COMMENT '身份证号',
    id_type ENUM('ID_CARD', 'PASSPORT', 'HK_MACAU_PASS', 'FOREIGN_RESIDENT') DEFAULT 'ID_CARD' COMMENT '证件类型',
    age INT COMMENT '年龄',
    FOREIGN KEY (reservation_id) REFERENCES museum_reservation(id)
);

-- 访客黑名单表
CREATE TABLE visitor_blacklist (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    id_card VARCHAR(18) UNIQUE NOT NULL COMMENT '身份证号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    blacklist_reason VARCHAR(255) COMMENT '拉黑原因',
    blacklist_date DATE NOT NULL COMMENT '拉黑日期',
    release_date DATE COMMENT '解除日期',
    violation_count INT DEFAULT 0 COMMENT '违规次数',
    status ENUM('ACTIVE', 'RELEASED') DEFAULT 'ACTIVE' COMMENT '状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 团体预约表
CREATE TABLE group_reservation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约ID',
    group_name VARCHAR(100) NOT NULL COMMENT '团体名称',
    group_type VARCHAR(50) COMMENT '团体类型',
    leader_name VARCHAR(50) NOT NULL COMMENT '带队人姓名',
    leader_phone VARCHAR(11) NOT NULL COMMENT '带队人电话',
    member_count INT NOT NULL COMMENT '成员数量',
    introduction_letter VARCHAR(255) COMMENT '介绍信',
    FOREIGN KEY (reservation_id) REFERENCES museum_reservation(id)
);

-- 系统配置表
CREATE TABLE museum_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(50) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '描述',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**需要删除的数据表**：
- vehicle_reservation（车辆预约表）
- vehicle_classification（车辆分类表）
- yard_info（车场信息表）
- month_ticket（月票表）
- device_info（设备信息表）
- maintenance（维修表）
- allocation（调拨表）
- scrap（报废表）

### 7.2 API接口设计

**预约管理接口**：
```javascript
// 个人预约
POST /api/museum/reservation/personal
{
    "visitorName": "张三",
    "visitorIdCard": "110101199001011234",
    "visitorPhone": "13800138000",
    "visitDate": "2024-03-15",
    "timeSlot": "09:00-10:00",
    "companions": [
        {
            "name": "李四",
            "idCard": "110101199002021234",
            "idType": "ID_CARD"
        }
    ]
}

// 团体预约
POST /api/museum/reservation/group
{
    "groupName": "某某学校",
    "groupType": "教育机构",
    "leaderName": "王老师",
    "leaderPhone": "13800138000",
    "visitDate": "2024-03-15",
    "timeSlot": "09:00-10:00",
    "memberCount": 30,
    "members": [...]
}

// 预约查询
GET /api/museum/reservation/query?idCard=110101199001011234&status=CONFIRMED

// 预约取消
PUT /api/museum/reservation/{id}/cancel

// 生成二维码
POST /api/museum/qrcode/generate/{reservationId}

// 黑名单检查
GET /api/museum/blacklist/check?idCard=110101199001011234
```

**后台管理接口**：
```javascript
// 预约审核
PUT /api/admin/museum/reservation/{id}/audit
{
    "status": "CONFIRMED",
    "auditRemark": "审核通过"
}

// 统计数据
GET /api/admin/museum/statistics?startDate=2024-03-01&endDate=2024-03-31

// 黑名单管理
POST /api/admin/museum/blacklist
{
    "idCard": "110101199001011234",
    "name": "张三",
    "reason": "连续2次逾期未取消"
}

// 系统配置
PUT /api/admin/museum/config
{
    "openTime": "09:00",
    "closeTime": "15:30",
    "maxPersonalReservation": 5,
    "blacklistDays": 60
}
```

### 7.3 前端组件设计

**预约日历组件**：
```vue
<template>
  <div class="reservation-calendar">
    <el-calendar v-model="selectedDate" @pick="handleDatePick">
      <template #dateCell="{ data }">
        <div class="calendar-cell" :class="getCellClass(data.day)">
          <span class="date">{{ data.day.split('-').slice(-1)[0] }}</span>
          <div class="time-slots" v-if="isAvailableDate(data.day)">
            <div
              v-for="slot in timeSlots"
              :key="slot.time"
              class="time-slot"
              :class="{ 'full': slot.remaining === 0 }"
              @click="selectTimeSlot(data.day, slot)"
            >
              <span>{{ slot.time }}</span>
              <span class="remaining">{{ slot.remaining }}</span>
            </div>
          </div>
        </div>
      </template>
    </el-calendar>
  </div>
</template>

<script>
export default {
  name: 'ReservationCalendar',
  data() {
    return {
      selectedDate: new Date(),
      timeSlots: [
        { time: '09:00-10:00', capacity: 100, remaining: 85 },
        { time: '10:00-11:00', capacity: 100, remaining: 92 },
        // ...
      ]
    }
  },
  methods: {
    isAvailableDate(date) {
      // 检查是否为可预约日期（周四、五、六）
      const dayOfWeek = new Date(date).getDay();
      return [4, 5, 6].includes(dayOfWeek);
    },
    getCellClass(date) {
      if (!this.isAvailableDate(date)) return 'unavailable';
      if (this.isPastDate(date)) return 'past';
      return 'available';
    },
    selectTimeSlot(date, slot) {
      if (slot.remaining > 0) {
        this.$emit('time-selected', { date, timeSlot: slot.time });
      }
    }
  }
}
</script>
```

**身份证验证组件**：
```vue
<template>
  <div class="id-card-validator">
    <el-form-item label="证件类型" prop="idType">
      <el-select v-model="form.idType" @change="handleTypeChange">
        <el-option label="身份证" value="ID_CARD" />
        <el-option label="护照" value="PASSPORT" />
        <el-option label="港澳通行证" value="HK_MACAU_PASS" />
        <el-option label="外国人永久居留证" value="FOREIGN_RESIDENT" />
      </el-select>
    </el-form-item>

    <el-form-item label="证件号码" prop="idCard">
      <el-input
        v-model="form.idCard"
        :placeholder="getPlaceholder()"
        @blur="validateIdCard"
        @input="handleInput"
      />
      <div v-if="validationResult.age" class="age-info">
        年龄：{{ validationResult.age }}岁
        <span v-if="validationResult.age < 14" class="warning">
          （14周岁以下不可单独预约）
        </span>
        <span v-if="validationResult.age >= 60" class="info">
          （60周岁以上无需预约）
        </span>
      </div>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'IDCardValidator',
  props: {
    modelValue: Object
  },
  data() {
    return {
      form: {
        idType: 'ID_CARD',
        idCard: ''
      },
      validationResult: {
        isValid: false,
        age: null,
        gender: null,
        birthDate: null
      }
    }
  },
  methods: {
    validateIdCard() {
      if (this.form.idType === 'ID_CARD') {
        const result = this.validateChineseIdCard(this.form.idCard);
        this.validationResult = result;
        this.$emit('validation-result', result);
      }
    },
    validateChineseIdCard(idCard) {
      // 身份证验证逻辑
      const reg18 = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;
      const reg15 = /^[1-9]\d{5}\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}$/;

      if (!reg18.test(idCard) && !reg15.test(idCard)) {
        return { isValid: false, error: '身份证格式不正确' };
      }

      // 提取生日和计算年龄
      let birthYear, birthMonth, birthDay;
      if (idCard.length === 18) {
        birthYear = parseInt(idCard.substr(6, 4));
        birthMonth = parseInt(idCard.substr(10, 2));
        birthDay = parseInt(idCard.substr(12, 2));
      } else {
        birthYear = parseInt('19' + idCard.substr(6, 2));
        birthMonth = parseInt(idCard.substr(8, 2));
        birthDay = parseInt(idCard.substr(10, 2));
      }

      const birthDate = new Date(birthYear, birthMonth - 1, birthDay);
      const age = this.calculateAge(birthDate);
      const gender = idCard.length === 18 ?
        (parseInt(idCard.substr(16, 1)) % 2 === 0 ? 'F' : 'M') :
        (parseInt(idCard.substr(14, 1)) % 2 === 0 ? 'F' : 'M');

      return {
        isValid: true,
        age,
        gender,
        birthDate,
        canReserveAlone: age >= 14,
        needReservation: age < 60
      };
    },
    calculateAge(birthDate) {
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      return age;
    }
  }
}
</script>
```

### 7.4 状态管理设计

**Vuex Store结构**：
```javascript
// store/modules/museum.js
export default {
  namespaced: true,
  state: {
    currentReservation: null,
    timeSlots: [],
    systemConfig: {},
    userReservations: [],
    blacklistStatus: false
  },
  mutations: {
    SET_CURRENT_RESERVATION(state, reservation) {
      state.currentReservation = reservation;
    },
    SET_TIME_SLOTS(state, slots) {
      state.timeSlots = slots;
    },
    SET_SYSTEM_CONFIG(state, config) {
      state.systemConfig = config;
    },
    SET_USER_RESERVATIONS(state, reservations) {
      state.userReservations = reservations;
    },
    SET_BLACKLIST_STATUS(state, status) {
      state.blacklistStatus = status;
    }
  },
  actions: {
    async createReservation({ commit }, reservationData) {
      try {
        const response = await api.createReservation(reservationData);
        commit('SET_CURRENT_RESERVATION', response.data);
        return response;
      } catch (error) {
        throw error;
      }
    },
    async checkBlacklist({ commit }, idCard) {
      try {
        const response = await api.checkBlacklist(idCard);
        commit('SET_BLACKLIST_STATUS', response.data.isBlacklisted);
        return response.data;
      } catch (error) {
        throw error;
      }
    },
    async loadTimeSlots({ commit }, date) {
      try {
        const response = await api.getTimeSlots(date);
        commit('SET_TIME_SLOTS', response.data);
        return response.data;
      } catch (error) {
        throw error;
      }
    }
  },
  getters: {
    availableTimeSlots: state => {
      return state.timeSlots.filter(slot => slot.remaining > 0);
    },
    canMakeReservation: state => {
      return !state.blacklistStatus && state.systemConfig.reservationEnabled;
    }
  }
}
```

## 八、部署和运维方案

### 8.1 环境配置
- **开发环境**：Vue CLI Dev Server + Mock API
- **测试环境**：Nginx + Node.js API Server
- **生产环境**：Nginx + PM2 + Redis + MySQL

### 8.2 性能优化
- 路由懒加载
- 组件按需引入
- 图片压缩和CDN
- API接口缓存
- 数据库索引优化

### 8.3 监控和日志
- 前端错误监控
- API性能监控
- 用户行为分析
- 系统日志记录

## 九、总结

本改造方案基于现有停车管理系统，通过删除无关功能、改造适用功能、新增博物馆特色功能的方式，将系统转换为博物馆预约管理系统。方案充分利用了现有的技术架构和通用功能模块，降低了开发成本和风险，同时确保了系统的稳定性和可维护性。

**核心优势**：
1. 复用现有成熟的用户管理和权限系统
2. 保持技术栈一致性，降低学习成本
3. 分阶段开发，风险可控
4. 功能完整，满足博物馆预约的所有需求

**预期效果**：
- 开发周期：8周
- 代码复用率：40%
- 功能完整度：100%
- 系统稳定性：高
