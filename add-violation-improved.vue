<template>
	<view class="container">
		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 车牌信息区域 -->
			<view class="section-card">
				<view class="section-header">
					<view class="header-icon">
						<text class="icon-emoji">🚗</text>
					</view>
					<text class="section-title">车牌信息</text>
				</view>
				<view class="input-group">
					<!-- 车牌号输入区域 -->
					<view class="plate-input-section">
						<view class="input-actions-container">
							<!-- 搜索框容器 -->
							<view class="plate-search-container">
								<view class="search-input-wrapper">
									<input
										class="plate-search-input"
										v-model="formData.plateNumber"
										placeholder="请输入车牌号码"
										@input="onPlateNumberInput"
										@focus="onPlateSearchFocus"
										@blur="onPlateSearchBlur"
										maxlength="8"
									/>
									<view class="search-icon" v-if="!formData.plateNumber">
										<u-icon name="search" size="20" color="#c8c9cc"></u-icon>
									</view>
									<view class="clear-btn" v-if="formData.plateNumber" @click="clearPlateNumber">
										<u-icon name="close-circle-fill" size="20" color="#c8c9cc"></u-icon>
									</view>
								</view>

								<!-- 搜索建议 -->
								<view class="plate-suggestions" v-if="showPlateSuggestions && plateSuggestions.length > 0">
									<view
										class="suggestion-item"
										v-for="(suggestion, index) in plateSuggestions"
										:key="index"
										@click="selectPlateSuggestion(suggestion)"
									>
										<view class="suggestion-icon">
											<text class="icon-emoji">🚗</text>
										</view>
										<text class="suggestion-text">{{ suggestion.plateNumber }}</text>
										<text class="suggestion-owner" v-if="suggestion.ownerName">{{ suggestion.ownerName }}</text>
									</view>
								</view>
							</view>

							<!-- 车牌识别按钮 -->
							<view class="recognition-btn-container">
								<view class="plate-recognition-btn" @click="openPlateRecognition">
									<u-icon name="camera" size="24" color="#2979ff"></u-icon>
									<text class="btn-label">识别</text>
								</view>
							</view>
						</view>
					</view>
					<!-- 车主信息显示 -->
					<view class="owner-info" v-if="ownerInfo">
						<view class="info-item">
							<text class="info-label">车主：</text>
							<text class="info-value">{{ ownerInfo.name }}</text>
						</view>
						<view class="info-item">
							<text class="info-label">电话：</text>
							<text class="info-value">{{ ownerInfo.phone }}</text>
						</view>
						<view class="info-item" v-if="ownerInfo.address">
							<text class="info-label">住址：</text>
							<text class="info-value">{{ ownerInfo.address }}</text>
						</view>
						<view class="info-item" v-if="ownerInfo.creditScore">
							<text class="info-label">信用分：</text>
							<text class="info-value" :class="getCreditScoreClass(ownerInfo.creditScore)">
								{{ ownerInfo.creditScore }}分
							</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 违规类型区域 -->
			<view class="section-card">
				<view class="section-header">
					<view class="header-icon">
						<text class="icon-emoji">⚠️</text>
					</view>
					<text class="section-title">违规类型</text>
				</view>
				<view class="violation-types">
					<!-- 已选择的类型显示 -->
					<view class="selected-type" v-if="selectedTypeInfo">
						<view class="selected-display">
							<text class="selected-icon">{{ selectedTypeInfo.icon }}</text>
							<text class="selected-name">{{ selectedTypeInfo.name }}</text>
							<text class="selected-check">✓</text>
						</view>
						<view class="divider"></view>
					</view>

					<!-- 搜索框 -->
					<view class="search-section" v-if="uiState.isSearching">
						<view class="search-container">
							<view class="search-box">
								<text class="search-icon">🔍</text>
								<input class="search-input" v-model="uiState.searchKeyword" placeholder="搜索违规类型..."
									@input="onSearchInput" focus />
								<text class="search-clear" v-if="uiState.searchKeyword" @click="clearSearch">×</text>
							</view>
							<view class="search-cancel" @click="cancelSearch">
								<text class="cancel-text">取消</text>
							</view>
						</view>
					</view>

					<!-- 搜索结果 -->
					<view class="search-results" v-if="uiState.isSearching && uiState.searchKeyword">
						<view class="section-label">搜索结果</view>
						<view class="type-tags" v-if="searchResults.length > 0">
							<view class="type-tag" v-for="(type, index) in searchResults" :key="index"
								@click="selectType(type)">
								<text class="tag-icon">{{ type.icon }}</text>
								<text class="tag-text">{{ type.name }}</text>
							</view>
						</view>
						<!-- 无搜索结果提示 -->
						<view class="no-results" v-if="searchResults.length === 0">
							<text class="no-results-icon">🔍</text>
							<text class="no-results-text">未找到匹配的违规类型</text>
							<text class="no-results-tip">试试其他关键词或选择下方常用类型</text>
						</view>
						<view class="divider" v-if="searchResults.length > 0"></view>
					</view>

					<!-- 常用类型 -->
					<view class="common-section">
						<view class="section-label">{{ selectedTypeInfo ? '其他常用类型' : '常用类型' }}</view>
						<view class="type-tags">
							<view class="type-tag" v-for="(type, index) in displayCommonTypes" :key="index"
								@click="selectType(type)">
								<text class="tag-icon">{{ type.icon }}</text>
								<text class="tag-text">{{ type.name }}</text>
							</view>
							<view class="type-tag more" @click="toggleMoreTypes">
								<text class="tag-text">{{ uiState.showMoreTypes ? '收起 ▲' : '更多类型 ▼' }}</text>
							</view>
						</view>
					</view>

					<!-- 其他类型（展开时显示） -->
					<view class="others-section" v-if="uiState.showMoreTypes">
						<view class="divider"></view>
						<view class="section-label">其他类型</view>
						<view class="type-tags">
							<view class="type-tag" v-for="(type, index) in violationConfig.others" :key="index"
								:class="{ selected: formData.violationType === type.value }"
								@click="selectType(type)">
								<text class="tag-icon">{{ type.icon }}</text>
								<text class="tag-text">{{ type.name }}</text>
							</view>
						</view>
					</view>

					<!-- 搜索入口 -->
					<view class="find-entry" v-if="!uiState.isSearching">
						<view class="divider"></view>
						<view class="find-trigger" @click="startSearch">
							<text class="find-icon">🔍</text>
							<text class="find-text">查找其他违规类型...</text>
						</view>
					</view>

					<!-- 自定义违规类型输入 -->
					<view class="custom-type-input" v-if="formData.violationType === 'other'">
						<input class="custom-input" v-model="formData.customType" placeholder="请输入自定义违规类型"
							maxlength="50" />
					</view>
				</view>
			</view>

			<!-- 违规位置区域 -->
			<view class="section-card">
				<view class="section-header">
					<view class="header-icon">
						<text class="icon-emoji">📍</text>
					</view>
					<text class="section-title">违规位置</text>
				</view>
				<view class="location-input-group">
					<view class="location-wrapper">
						<input class="location-input" v-model="formData.location" placeholder="请输入违规位置"
							maxlength="100" />
					</view>
					<!-- 常用位置快捷选择 -->
					<view class="common-locations" v-if="commonLocations.length > 0">
						<view class="location-label">常用位置：</view>
						<view class="location-tags">
							<view class="location-tag" v-for="(location, index) in commonLocations" :key="index"
								@click="selectLocation(location)">
								<text class="tag-text">{{ location }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 现场取证区域 -->
			<view class="section-card">
				<view class="section-header">
					<view class="header-icon">
						<text class="icon-emoji">📸</text>
					</view>
					<text class="section-title">现场取证</text>
				</view>
				<view class="evidence-section">
					<!-- 照片上传 -->
					<view class="photo-upload">
						<view class="upload-header">
							<text class="upload-title">拍照取证</text>
							<text class="photo-count">{{ formData.photos.length }}/6</text>
						</view>
						<view class="photo-grid">
							<view class="photo-item" v-for="(photo, index) in formData.photos" :key="index"
								@click="previewPhoto(index)">
								<image :src="photo" mode="aspectFill" class="photo-image"></image>
								<view class="photo-delete" @click.stop="deletePhoto(index)">
									<text class="icon-emoji">×</text>
								</view>
							</view>
							<view class="photo-add" v-if="formData.photos.length < 6" @click="takePhoto">
								<text class="icon-emoji add-icon">📷</text>
								<text class="add-text">拍照</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 违规描述区域 -->
			<view class="section-card">
				<view class="section-header">
					<view class="header-icon">
						<text class="icon-emoji">📝</text>
					</view>
					<text class="section-title">违规描述</text>
				</view>
				<view class="description-input">
					<textarea class="description-textarea" v-model="formData.description"
						placeholder="请详细描述违规情况..." maxlength="200" :show-word-limit="true"
						:auto-height="true"></textarea>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<view class="submit-btn" :class="{ disabled: !canSubmit }" @click="submitViolation">
					<text>{{ submitting ? '提交中...' : '提交违规记录' }}</text>
				</view>
			</view>
		</view>

		<!-- 车牌识别弹窗 -->
		<u-modal
			:show="showPlateRecognitionModal"
			title="车牌识别"
			:show-cancel-button="true"
			:show-confirm-button="false"
			@cancel="closePlateRecognition"
		>
			<view class="plate-recognition-modal">
				<!-- 识别选择界面 -->
				<view class="recognition-options" v-if="!showCamera && !recognitionResult">
					<view class="option-item" @click="openCamera">
						<view class="option-icon">
							<u-icon name="camera" size="40" color="#2979ff"></u-icon>
						</view>
						<view class="option-content">
							<text class="option-title">摄像头识别</text>
							<text class="option-desc">实时扫描车牌号码</text>
						</view>
					</view>

					<view class="option-item" @click="chooseFromAlbum">
						<view class="option-icon">
							<u-icon name="photo" size="40" color="#19be6b"></u-icon>
						</view>
						<view class="option-content">
							<text class="option-title">相册选择</text>
							<text class="option-desc">从相册选择车牌图片</text>
						</view>
					</view>
				</view>

				<!-- 识别结果显示 -->
				<view class="recognition-result" v-if="recognitionResult">
					<view class="result-header">
						<u-icon name="checkmark-circle" size="40" color="#19be6b"></u-icon>
						<text class="result-title">识别成功</text>
					</view>
					<view class="result-plate">
						<text class="plate-text">{{ recognitionResult.plateNumber }}</text>
						<text class="plate-color" v-if="recognitionResult.color">{{ recognitionResult.color }}</text>
						<text class="plate-confidence" v-if="recognitionResult.confidence">置信度: {{ recognitionResult.confidence }}%</text>
					</view>
					<view class="result-actions">
						<view class="action-btn use-btn" @click="useRecognitionResult">
							<text>使用此结果</text>
						</view>
						<view class="action-btn retry-btn" @click="retryRecognition">
							<text>重新识别</text>
						</view>
					</view>
				</view>
			</view>
		</u-modal>

		<!-- 提交确认弹窗 -->
		<view class="confirm-modal" v-if="showConfirmModal" @click="closeConfirmModal">
			<view class="confirm-content" @click.stop>
				<view class="confirm-header">
					<text class="confirm-title">确认提交</text>
				</view>
				<view class="confirm-body">
					<text class="confirm-text">确认提交违规记录吗？</text>
					<view class="confirm-info">
						<text class="info-text">车牌：{{ formData.plateNumber }}</text>
						<text class="info-text">类型：{{ getViolationTypeName() }}</text>
						<text class="info-text">位置：{{ formData.location }}</text>
					</view>
				</view>
				<view class="confirm-actions">
					<view class="confirm-btn cancel" @click="closeConfirmModal">
						<text>取消</text>
					</view>
					<view class="confirm-btn primary" @click="confirmSubmit">
						<text>{{ submitting ? '提交中...' : '确认提交' }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentUserRole: 'manager',
			formData: {
				plateNumber: '',
				violationType: '',
				customType: '',
				location: '',
				photos: [],
				description: ''
			},
			ownerInfo: null,

			// 违规类型配置 - 基于真实数据
			violationConfig: {
				// 常用类型（基于使用频率统计）
				common: [
					{ name: '超时停车', value: 'overtime', icon: '🚗', usage: 45 },
					{ name: '未按位停车', value: 'wrong_position', icon: '🅿️', usage: 30 },
					{ name: '占用他人车位', value: 'occupy_space', icon: '🚫', usage: 15 },
					{ name: '遮挡车牌', value: 'block_plate', icon: '🚫', usage: 12 },
					{ name: '堵塞消防通道', value: 'block_passage', icon: '🚧', usage: 10 },
					{ name: '压线停车', value: 'cross_line', icon: '📏', usage: 8 }
				],
				// 其他类型
				others: [
					{ name: '未经授权停车', value: 'unauthorized', icon: '🔒', usage: 8 },
					{ name: '占用残疾人车位', value: 'disabled_space', icon: '♿', usage: 4 },
					{ name: '逆向停车', value: 'reverse_parking', icon: '🔄', usage: 3 },
					{ name: '跨车位停车', value: 'cross_parking', icon: '📐', usage: 3 },
					{ name: '占用VIP车位', value: 'vip_space', icon: '👑', usage: 2 },
					{ name: '未熄火停车', value: 'engine_on', icon: '🔥', usage: 2 },
					{ name: '占用卸货区', value: 'loading_zone', icon: '📦', usage: 2 },
					{ name: '超宽停车', value: 'oversized', icon: '📏', usage: 1 },
					{ name: '占用绿化带', value: 'green_belt', icon: '🌱', usage: 1 },
					{ name: '占用充电桩车位', value: 'charging_space', icon: '🔌', usage: 1 },
					{ name: '车辆损坏', value: 'vehicle_damage', icon: '🔧', usage: 1 },
					{ name: '其他', value: 'other', icon: '➕', usage: 1 }
				]
			},

			// 常用位置 - 基于真实数据
			commonLocations: [
				'A区-15号车位', 'B区-08号车位', 'C区-22号车位', 'D区-05号车位',
				'E区-新能源专用位', 'A区-03号车位', 'B区-12号车位', 'C区-18号车位'
			],

			// 界面状态
			uiState: {
				showMoreTypes: false,
				searchKeyword: '',
				isSearching: false
			},

			// 搜索相关
			searchTimer: null,
			showConfirmModal: false,
			submitting: false,

			// 车牌搜索相关
			showPlateSuggestions: false,
			plateSuggestions: [],
			plateSearchTimer: null,

			// 车牌识别相关
			showPlateRecognitionModal: false,
			showCamera: false,
			isRecognizing: false,
			recognitionResult: null
		}
	},

	// 添加组件销毁时的清理
	beforeDestroy() {
		// 清理搜索定时器
		if (this.searchTimer) {
			clearTimeout(this.searchTimer);
			this.searchTimer = null;
		}
		// 清理车牌搜索定时器
		if (this.plateSearchTimer) {
			clearTimeout(this.plateSearchTimer);
			this.plateSearchTimer = null;
		}
	},

	computed: {
		canSubmit() {
			return this.formData.plateNumber &&
				   this.formData.violationType &&
				   this.formData.location &&
				   (this.formData.violationType !== 'other' || this.formData.customType);
		},

		// 当前显示的常用类型（排除已选择的）
		displayCommonTypes() {
			return this.violationConfig.common.filter(type =>
				type.value !== this.formData.violationType
			);
		},

		// 搜索结果
		searchResults() {
			if (!this.uiState.searchKeyword) return [];

			const keyword = this.uiState.searchKeyword.toLowerCase();
			const allTypes = [...this.violationConfig.common, ...this.violationConfig.others];

			return allTypes.filter(type =>
				type.name.toLowerCase().includes(keyword) ||
				type.value.toLowerCase().includes(keyword)
			);
		},

		// 已选择的类型信息
		selectedTypeInfo() {
			if (!this.formData.violationType) return null;

			const allTypes = [...this.violationConfig.common, ...this.violationConfig.others];
			return allTypes.find(type => type.value === this.formData.violationType);
		}
	},

	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// ================ 车牌搜索框相关方法 ================

		// 车牌号输入事件
		onPlateNumberInput(e) {
			const value = e.detail.value || e.target.value || '';
			this.formData.plateNumber = value.toUpperCase(); // 转换为大写

			// 清除之前的定时器
			if (this.plateSearchTimer) {
				clearTimeout(this.plateSearchTimer);
			}

			// 防抖搜索
			this.plateSearchTimer = setTimeout(() => {
				this.generatePlateSuggestions(value);
				this.onPlateNumberChange(); // 查询车主信息
			}, 300);
		},

		// 车牌搜索框获得焦点
		onPlateSearchFocus() {
			if (this.formData.plateNumber) {
				this.generatePlateSuggestions(this.formData.plateNumber);
			}
		},

		// 车牌搜索框失去焦点
		onPlateSearchBlur() {
			// 延迟隐藏建议，以便用户可以点击建议项
			setTimeout(() => {
				this.showPlateSuggestions = false;
			}, 200);
		},

		// 清空车牌号
		clearPlateNumber() {
			this.formData.plateNumber = '';
			this.showPlateSuggestions = false;
			this.ownerInfo = null;
		},

		// 生成车牌搜索建议 - 基于真实数据
		generatePlateSuggestions(keyword) {
			if (!keyword || keyword.length < 2) {
				this.showPlateSuggestions = false;
				return;
			}

			// 真实车牌数据库
			const realPlateData = [
				{ plateNumber: '黑A12345', ownerName: '张三' },
				{ plateNumber: '黑A34567', ownerName: '张三' },
				{ plateNumber: '黑A45678', ownerName: '张三' },
				{ plateNumber: '黑B67890', ownerName: '黄巢' },
				{ plateNumber: '黑A01234', ownerName: '萧燕燕' },
				{ plateNumber: '黑A24680', ownerName: '赵光义' },
				{ plateNumber: '黑AF57913', ownerName: '王小川' },
				{ plateNumber: '黑A54321', ownerName: '赵六' },
				{ plateNumber: '黑B98765', ownerName: '钱七' },
				{ plateNumber: '黑C11111', ownerName: '孙八' }
			];

			// 过滤匹配的车牌
			const suggestions = realPlateData.filter(item =>
				item.plateNumber.toLowerCase().includes(keyword.toLowerCase())
			).slice(0, 5); // 最多显示5个建议

			this.plateSuggestions = suggestions;
			this.showPlateSuggestions = suggestions.length > 0;
		},

		// 选择车牌建议
		selectPlateSuggestion(suggestion) {
			this.formData.plateNumber = suggestion.plateNumber;
			this.showPlateSuggestions = false;
			this.onPlateNumberChange(); // 查询车主信息
		},

		// 车牌号输入变化
		async onPlateNumberChange() {
			if (this.formData.plateNumber && this.formData.plateNumber.length >= 7) {
				try {
					this.ownerInfo = await this.getOwnerInfo(this.formData.plateNumber);
				} catch (error) {
					this.ownerInfo = null;
				}
			} else {
				this.ownerInfo = null;
			}
		},

		// 获取车主信息 - 基于真实数据
		async getOwnerInfo(plateNumber) {
			return new Promise((resolve) => {
				setTimeout(() => {
					const realOwnerData = {
						'黑A12345': {
							name: '张三',
							phone: '138****5678',
							address: '8栋5单元555室',
							creditScore: 85
						},
						'黑A34567': {
							name: '张三',
							phone: '138****5678',
							address: '8栋5单元555室',
							creditScore: 85
						},
						'黑A45678': {
							name: '张三',
							phone: '138****5678',
							address: '8栋5单元555室',
							creditScore: 85
						},
						'黑B67890': {
							name: '黄巢',
							phone: '137****4478',
							address: '5栋1单元105室',
							creditScore: 92
						},
						'黑A01234': {
							name: '萧燕燕',
							phone: '138****4478',
							address: '2栋7单元105室',
							creditScore: 78
						},
						'黑A24680': {
							name: '赵光义',
							phone: '138****4478',
							address: '25栋7单元155室',
							creditScore: 88
						},
						'黑AF57913': {
							name: '王小川',
							phone: '135****8574',
							address: '15栋4单元707室',
							creditScore: 72
						}
					};
					resolve(realOwnerData[plateNumber] || null);
				}, 500);
			});
		},

		// 获取信用分样式类
		getCreditScoreClass(score) {
			if (score >= 80) return 'credit-excellent';
			if (score >= 60) return 'credit-warning';
			return 'credit-danger';
		},

		// ================ 车牌识别相关方法 ================

		// 打开车牌识别
		openPlateRecognition() {
			console.log('🎯 [车牌识别] 打开车牌识别弹窗');
			this.showPlateRecognitionModal = true;
			this.showCamera = false;
			this.recognitionResult = null;
			this.isRecognizing = false;
		},

		// 关闭车牌识别
		closePlateRecognition() {
			this.showPlateRecognitionModal = false;
			this.showCamera = false;
			this.recognitionResult = null;
			this.isRecognizing = false;
		},

		// 打开摄像头
		openCamera() {
			this.showCamera = true;
		},

		// 从相册选择图片
		chooseFromAlbum() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album'],
				success: (res) => {
					this.recognizeFromImage(res.tempFilePaths[0]);
				}
			});
		},

		// 从图片识别车牌
		async recognizeFromImage(imagePath) {
			uni.showLoading({ title: '识别中...' });

			try {
				// 模拟识别结果（实际应调用真实API）
				await new Promise(resolve => setTimeout(resolve, 2000));

				// 模拟识别成功
				const mockResults = ['黑A12345', '黑B67890', '黑AF57913', '黑A01234'];
				const randomResult = mockResults[Math.floor(Math.random() * mockResults.length)];

				this.recognitionResult = {
					plateNumber: randomResult,
					color: randomResult.includes('AF') ? '绿牌' : '蓝牌',
					confidence: 85 + Math.floor(Math.random() * 15)
				};

				this.showCamera = false;
				console.log(`✅ 识别成功: ${randomResult}`);
			} catch (error) {
				console.error('识别失败:', error);
				uni.showToast({
					title: '识别失败，请重试',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
				this.isRecognizing = false;
			}
		},

		// 使用识别结果
		useRecognitionResult() {
			if (this.recognitionResult && this.recognitionResult.plateNumber) {
				this.formData.plateNumber = this.recognitionResult.plateNumber;
				this.closePlateRecognition();
				this.onPlateNumberChange(); // 查询车主信息

				uni.showToast({
					title: '已使用识别结果',
					icon: 'success'
				});
			}
		},

		// 重新识别
		retryRecognition() {
			this.recognitionResult = null;
			this.showCamera = false;
		},

		// ================ 违规类型相关方法 ================

		// 选择违规类型
		selectType(type) {
			this.formData.violationType = type.value;
			if (type.value !== 'other') {
				this.formData.customType = '';
			}

			// 选择后收起展开状态和搜索状态
			this.uiState.showMoreTypes = false;
			this.uiState.isSearching = false;
			this.uiState.searchKeyword = '';
		},

		// 切换更多类型显示
		toggleMoreTypes() {
			this.uiState.showMoreTypes = !this.uiState.showMoreTypes;
			if (!this.uiState.showMoreTypes) {
				this.uiState.isSearching = false;
				this.uiState.searchKeyword = '';
			}
		},

		// 开始搜索
		startSearch() {
			this.uiState.isSearching = true;
			this.uiState.showMoreTypes = true;
		},

		// 搜索输入处理（带防抖）
		onSearchInput() {
			// 清除之前的定时器
			if (this.searchTimer) {
				clearTimeout(this.searchTimer);
			}

			// 设置新的防抖定时器
			this.searchTimer = setTimeout(() => {
				// 如果搜索关键词为空，显示提示
				if (!this.uiState.searchKeyword.trim()) {
					console.log('🔍 搜索关键词为空');
					return;
				}

				// 执行搜索逻辑
				console.log('🔍 搜索关键词:', this.uiState.searchKeyword);

				// 如果没有搜索结果，可以显示提示
				if (this.searchResults.length === 0) {
					console.log('🔍 没有找到匹配的违规类型');
				}
			}, 300); // 300ms 防抖延迟
		},

		// 清空搜索
		clearSearch() {
			this.uiState.searchKeyword = '';
			if (this.searchTimer) {
				clearTimeout(this.searchTimer);
				this.searchTimer = null;
			}
		},

		// 取消搜索
		cancelSearch() {
			this.clearSearch();
			this.uiState.isSearching = false;
		},

		// 获取违规类型名称
		getViolationTypeName() {
			if (this.formData.violationType === 'other') {
				return this.formData.customType || '其他';
			}
			const allTypes = [...this.violationConfig.common, ...this.violationConfig.others];
			const type = allTypes.find(t => t.value === this.formData.violationType);
			return type ? type.name : '';
		},

		// ================ 位置相关方法 ================



		// 选择常用位置
		selectLocation(location) {
			this.formData.location = location;
		},

		// ================ 照片相关方法 ================

		// 拍照
		takePhoto() {
			uni.chooseImage({
				count: 6 - this.formData.photos.length,
				sizeType: ['compressed'],
				sourceType: ['camera'],
				success: (res) => {
					this.formData.photos.push(...res.tempFilePaths);
				}
			});
		},

		// 预览照片
		previewPhoto(index) {
			uni.previewImage({
				urls: this.formData.photos,
				current: index
			});
		},

		// 删除照片
		deletePhoto(index) {
			this.formData.photos.splice(index, 1);
		},

		// ================ 提交相关方法 ================

		// 提交违规记录
		submitViolation() {
			if (!this.canSubmit) {
				uni.showToast({
					title: '请完善必填信息',
					icon: 'none'
				});
				return;
			}
			this.showConfirmModal = true;
		},

		// 关闭确认弹窗
		closeConfirmModal() {
			this.showConfirmModal = false;
		},

		// 确认提交
		async confirmSubmit() {
			this.submitting = true;
			try {
				await this.submitToServer();
				uni.showToast({
					title: '提交成功',
					icon: 'success'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			} catch (error) {
				uni.showToast({
					title: '提交失败',
					icon: 'none'
				});
			} finally {
				this.submitting = false;
				this.closeConfirmModal();
			}
		},

		// 提交到服务器
		async submitToServer() {
			// 构建提交数据
			const submitData = {
				plateNumber: this.formData.plateNumber,
				violationType: this.formData.violationType,
				customType: this.formData.customType,
				location: this.formData.location,
				description: this.formData.description,
				photos: this.formData.photos,
				ownerInfo: this.ownerInfo,
				reportTime: new Date().toISOString(),
				reporterId: this.currentUserRole === 'manager' ? 1 : null
			};

			console.log('提交数据:', submitData);

			// 模拟API调用
			return new Promise((resolve, reject) => {
				setTimeout(() => {
					// 模拟成功
					if (Math.random() > 0.1) {
						resolve({
							success: true,
							id: Date.now(),
							message: '违规记录创建成功'
						});
					} else {
						reject(new Error('网络错误'));
					}
				}, 2000);
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: #f5f6fa;
	width: 100%;
}

/* 页面内容 */
.page-content {
	padding-top: 12rpx;
	padding-bottom: 24rpx;
	padding-left: 16rpx;
	padding-right: 16rpx;
}

/* 卡片样式 */
.section-card {
	background: #ffffff;
	border-radius: 10rpx;
	margin-bottom: 12rpx;
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

.section-header {
	display: flex;
	align-items: center;
	padding: 20rpx 20rpx 12rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.header-icon {
	width: 36rpx;
	height: 36rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 10rpx;
}

.header-icon .icon-emoji {
	font-size: 28rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
}

/* 车牌信息样式 */
.input-group {
	padding: 20rpx;
}

.plate-input-section {
	margin-bottom: 16rpx;
}

.input-actions-container {
	display: flex;
	align-items: flex-start;
	gap: 12rpx;
}

.plate-search-container {
	flex: 1;
	position: relative;
}

.search-input-wrapper {
	position: relative;
	background: #f8f9fa;
	border-radius: 8rpx;
	border: 2rpx solid #e9ecef;
	transition: border-color 0.3s ease;
}

.search-input-wrapper:focus-within {
	border-color: #2979ff;
}

.plate-search-input {
	width: 100%;
	height: 68rpx;
	padding: 0 40rpx 0 16rpx;
	font-size: 28rpx;
	color: #333333;
	background: transparent;
}

.search-icon, .clear-btn {
	position: absolute;
	right: 16rpx;
	top: 50%;
	transform: translateY(-50%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.clear-btn {
	cursor: pointer;
}

.recognition-btn-container {
	flex-shrink: 0;
}

.plate-recognition-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 80rpx;
	height: 68rpx;
	background: #f0f8ff;
	border: 2rpx solid #2979ff;
	border-radius: 8rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.plate-recognition-btn:active {
	background: #e6f3ff;
	transform: scale(0.95);
}

.btn-label {
	font-size: 20rpx;
	color: #2979ff;
	margin-top: 4rpx;
}

/* 车牌搜索建议 */
.plate-suggestions {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background: #ffffff;
	border-radius: 8rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
	z-index: 1000;
	max-height: 300rpx;
	overflow-y: auto;
}

.suggestion-item {
	display: flex;
	align-items: center;
	padding: 16rpx;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.suggestion-item:hover {
	background: #f8f9fa;
}

.suggestion-item:last-child {
	border-bottom: none;
}

.suggestion-icon {
	margin-right: 12rpx;
}

.suggestion-text {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
	margin-right: 12rpx;
}

.suggestion-owner {
	font-size: 24rpx;
	color: #666666;
}

/* 车主信息样式 */
.owner-info {
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 16rpx;
	margin-top: 12rpx;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.info-item:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 24rpx;
	color: #666666;
	width: 120rpx;
	flex-shrink: 0;
}

.info-value {
	font-size: 26rpx;
	color: #333333;
	font-weight: 500;
}

.credit-excellent {
	color: #52c41a;
}

.credit-warning {
	color: #faad14;
}

.credit-danger {
	color: #ff4d4f;
}

/* 违规类型样式 */
.violation-types {
	padding: 20rpx;
}

.selected-type {
	margin-bottom: 16rpx;
}

.selected-display {
	display: flex;
	align-items: center;
	padding: 16rpx;
	background: #e6f7ff;
	border: 2rpx solid #1890ff;
	border-radius: 8rpx;
}

.selected-icon {
	font-size: 32rpx;
	margin-right: 12rpx;
}

.selected-name {
	flex: 1;
	font-size: 28rpx;
	color: #1890ff;
	font-weight: 600;
}

.selected-check {
	font-size: 28rpx;
	color: #52c41a;
	font-weight: bold;
}

.divider {
	height: 1rpx;
	background: #f0f0f0;
	margin: 16rpx 0;
}

/* 搜索区域 */
.search-section {
	margin-bottom: 16rpx;
}

.search-container {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.search-box {
	flex: 1;
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 0 16rpx;
	height: 64rpx;
}

.search-icon {
	font-size: 28rpx;
	margin-right: 12rpx;
	color: #999999;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	background: transparent;
}

.search-clear {
	font-size: 32rpx;
	color: #999999;
	cursor: pointer;
	padding: 8rpx;
}

.search-cancel {
	padding: 16rpx 0;
}

.cancel-text {
	font-size: 28rpx;
	color: #666666;
}

/* 搜索结果 */
.search-results {
	margin-bottom: 16rpx;
}

.section-label {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 12rpx;
}

.no-results {
	text-align: center;
	padding: 40rpx 20rpx;
}

.no-results-icon {
	font-size: 48rpx;
	margin-bottom: 16rpx;
	display: block;
}

.no-results-text {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 8rpx;
	display: block;
}

.no-results-tip {
	font-size: 24rpx;
	color: #999999;
	display: block;
}

/* 类型标签 */
.type-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.type-tag {
	display: flex;
	align-items: center;
	padding: 12rpx 16rpx;
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	min-height: 60rpx;
}

.type-tag:active {
	transform: scale(0.95);
}

.type-tag.selected {
	background: #e6f7ff;
	border-color: #1890ff;
}

.type-tag.more {
	background: #ffffff;
	border-color: #d9d9d9;
	border-style: dashed;
}

.tag-icon {
	font-size: 28rpx;
	margin-right: 8rpx;
}

.tag-text {
	font-size: 26rpx;
	color: #333333;
	white-space: nowrap;
}

.type-tag.selected .tag-text {
	color: #1890ff;
	font-weight: 600;
}

.type-tag.more .tag-text {
	color: #666666;
}

/* 查找入口 */
.find-entry {
	margin-top: 16rpx;
}

.find-trigger {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 16rpx;
	background: #fafafa;
	border-radius: 8rpx;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.find-trigger:active {
	background: #f0f0f0;
}

.find-icon {
	font-size: 28rpx;
	margin-right: 8rpx;
	color: #666666;
}

.find-text {
	font-size: 26rpx;
	color: #666666;
}

/* 自定义类型输入 */
.custom-type-input {
	margin-top: 16rpx;
}

.custom-input {
	width: 100%;
	height: 64rpx;
	padding: 0 16rpx;
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
}

.custom-input:focus {
	border-color: #2979ff;
}

/* 位置输入样式 */
.location-input-group {
	padding: 20rpx;
}

.location-wrapper {
	margin-bottom: 16rpx;
}

.location-input {
	width: 100%;
	height: 68rpx;
	padding: 0 16rpx;
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	box-sizing: border-box;
}

.location-input:focus {
	border-color: #2979ff;
}



.common-locations {
	margin-top: 12rpx;
}

.location-label {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 12rpx;
}

.location-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.location-tag {
	padding: 8rpx 12rpx;
	background: #f0f0f0;
	border-radius: 6rpx;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.location-tag:active {
	background: #e0e0e0;
}

.location-tag .tag-text {
	font-size: 24rpx;
	color: #666666;
}

/* 现场取证样式 */
.evidence-section {
	padding: 20rpx;
}

.photo-upload {
	margin-bottom: 16rpx;
}

.upload-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.upload-title {
	font-size: 26rpx;
	color: #333333;
	font-weight: 500;
}

.photo-count {
	font-size: 24rpx;
	color: #666666;
}

.photo-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 12rpx;
}

.photo-item {
	position: relative;
	aspect-ratio: 1;
	border-radius: 8rpx;
	overflow: hidden;
}

.photo-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.photo-delete {
	position: absolute;
	top: 4rpx;
	right: 4rpx;
	width: 32rpx;
	height: 32rpx;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.photo-delete .icon-emoji {
	color: #ffffff;
	font-size: 20rpx;
	font-weight: bold;
}

.photo-add {
	aspect-ratio: 1;
	background: #f8f9fa;
	border: 2rpx dashed #d9d9d9;
	border-radius: 8rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.photo-add:active {
	background: #f0f0f0;
	border-color: #bfbfbf;
}

.add-icon {
	font-size: 40rpx;
	color: #999999;
	margin-bottom: 8rpx;
}

.add-text {
	font-size: 24rpx;
	color: #999999;
}

/* 描述输入样式 */
.description-input {
	padding: 20rpx;
}

.description-textarea {
	width: 100%;
	min-height: 120rpx;
	padding: 16rpx;
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #333333;
	line-height: 1.5;
	resize: none;
}

.description-textarea:focus {
	border-color: #2979ff;
}

/* 提交按钮样式 */
.submit-section {
	padding: 24rpx 16rpx;
	padding-bottom: 40rpx;
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

.submit-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.3);
}

.submit-btn.disabled {
	background: #d9d9d9;
	box-shadow: none;
	cursor: not-allowed;
}

.submit-btn text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: 600;
}

.submit-btn.disabled text {
	color: #999999;
}

/* 车牌识别弹窗样式 */
.plate-recognition-modal {
	padding: 20rpx;
}

.recognition-options {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.option-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.option-item:active {
	background: #f0f0f0;
	transform: scale(0.98);
}

.option-icon {
	margin-right: 16rpx;
}

.option-content {
	flex: 1;
}

.option-title {
	font-size: 28rpx;
	color: #333333;
	font-weight: 600;
	margin-bottom: 4rpx;
	display: block;
}

.option-desc {
	font-size: 24rpx;
	color: #666666;
	display: block;
}

.recognition-result {
	text-align: center;
	padding: 20rpx;
}

.result-header {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 24rpx;
}

.result-title {
	font-size: 28rpx;
	color: #333333;
	font-weight: 600;
	margin-top: 12rpx;
}

.result-plate {
	background: #f0f8ff;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 24rpx;
}

.plate-text {
	font-size: 36rpx;
	color: #1890ff;
	font-weight: bold;
	margin-bottom: 8rpx;
	display: block;
}

.plate-color {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 4rpx;
	display: block;
}

.plate-confidence {
	font-size: 22rpx;
	color: #999999;
	display: block;
}

.result-actions {
	display: flex;
	gap: 12rpx;
}

.action-btn {
	flex: 1;
	height: 72rpx;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.action-btn:active {
	transform: scale(0.95);
}

.use-btn {
	background: #1890ff;
}

.use-btn text {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: 600;
}

.retry-btn {
	background: #f0f0f0;
}

.retry-btn text {
	color: #666666;
	font-size: 28rpx;
}

/* 确认弹窗样式 */
.confirm-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.confirm-content {
	background: #ffffff;
	border-radius: 16rpx;
	margin: 40rpx;
	max-width: 600rpx;
	width: 100%;
	overflow: hidden;
}

.confirm-header {
	padding: 32rpx 24rpx 16rpx;
	text-align: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.confirm-title {
	font-size: 32rpx;
	color: #333333;
	font-weight: 600;
}

.confirm-body {
	padding: 24rpx;
	text-align: center;
}

.confirm-text {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.confirm-info {
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 16rpx;
}

.info-text {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 8rpx;
	display: block;
}

.info-text:last-child {
	margin-bottom: 0;
}

.confirm-actions {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
}

.confirm-btn {
	flex: 1;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.confirm-btn:first-child {
	border-right: 1rpx solid #f0f0f0;
}

.confirm-btn.cancel {
	background: #ffffff;
}

.confirm-btn.cancel:active {
	background: #f8f9fa;
}

.confirm-btn.cancel text {
	color: #666666;
	font-size: 28rpx;
}

.confirm-btn.primary {
	background: #1890ff;
}

.confirm-btn.primary:active {
	background: #096dd9;
}

.confirm-btn.primary text {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: 600;
}
</style>
